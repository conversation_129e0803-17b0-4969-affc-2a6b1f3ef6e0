# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/21 18:16
import collections
import json
import re

from app.basic.util import utils
from app.json_duplicate_check.get_similarity import FindSimilarTexts


"""
- 获取一个 json
- 遍历 json 的试题并把各字段内容合并，生成如下结构 [{node_id: node_str}, ...]
- 做重复检测，生成结构 {node_id1: [node_id2, node_id3], node_id4: [node_id5]}
"""


class NodeDuplicateCheck:
    def __init__(self, json_data, task_id):
        self.json_data = json_data
        self.task_id = task_id

    def main(self):
        node_str_list = self.get_node_str_list()
        similar_node_id_dict = self.get_similar_node(node_str_list)
        self.get_check_result(similar_node_id_dict)
        return self.json_data

    def get_node_str_list(self):
        node_str_list = []
        for item in utils.iter_tree(self.json_data):
            node = item.node
            node_type = node['node_type']
            if node_type != 'question':
                continue
            content = node['content']
            node_id = node['node_id']
            serial_number = node.get('serial_number', '')
            node_str = self.node_to_str(content)
            node_str_list.append({
                'uid': node_id,
                'text': node_str,
                'sn': serial_number
            })
        return node_str_list

    def get_similar_node(self, node_str_list):
        similar_list = FindSimilarTexts(node_str_list, target=0.8).main()
        similar_node_id_dict = collections.defaultdict(list)
        for item in similar_list:
            node_id1 = item['uid1'],
            similar_node_id_dict[node_id1].append({
                'node_id': item['uid2'],
                'sn': item['sn2'],
                'text': item['text2'],
                'similarity': item['similarity'],
            })
        return similar_node_id_dict

    def get_check_result(self, similar_node_id_dict):
        for item in utils.iter_tree(self.json_data):
            node = item.node
            node_id = node['node_id']
            node_type = node['node_type']
            if node_type != 'question':
                continue
            if node_id not in similar_node_id_dict:
                continue
            # 拼接相似度说明文本
            similar_list = similar_node_id_dict[node_id]
            similar_text_list = []
            for s in similar_list:
                similar_text_list.append(f"与第{s['sn']}题相似度{s['similarity']}")
            similar_text = '\n'.join(similar_text_list)
            # 拿到锚点
            node_str = json.dumps(node)
            tag = re.search(r'para\d+', node_str).group()
            item.node['error_info'].append({
                "id": tag,
                "type": "text",
                "error_code": 101,
                "level": 0,
                "error_info": similar_text,
                "fix_info": ""
            })

    def node_to_str(self, json_data):
        body_list = []
        choice_list = []
        answer_list = []
        analysis_list = []
        extra_list = []

        def recursion(data):
            has_children = bool(data.get('children'))
            # 题号
            body = data['body']
            if body:
                body_list.append(body)

            # 选项
            choice = data.get('choice', [])
            if choice:
                for c in choice:
                    letter, option = c['letter'] + '.', c['option']
                    choice_list.append(letter + option)

            # 答案
            answer = data['answer']
            answer = '、'.join(answer)
            if answer:
                answer_list.append(answer)

            # 解析
            analysis = data['analysis']
            if analysis:
                analysis_list.append(analysis)

            if has_children:
                for child in data['children']:
                    recursion(child)

        recursion(json_data)

        html_list = body_list + choice_list + answer_list + analysis_list + extra_list
        html_data = '\n'.join(html_list)
        res = re.sub(r'<[^<>]*?>', '', html_data)
        return res
