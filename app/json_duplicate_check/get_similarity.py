# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/21 18:17
from difflib import SequenceMatcher


class FindSimilarTexts:
    def __init__(self, text_list, target):
        self.text_list = text_list
        self.target = target

    def main(self):
        """
        找出相似度在 N% 以上的文本认为是相似文本
        """
        similar_list = []
        n = len(self.text_list)
        for i in range(n):
            for j in range(i + 1, n):
                text_item_1 = self.text_list[i]
                node_id_1, text_1 = text_item_1['node_id'], text_item_1['node_str']
                text_item_2 = self.text_list[j]
                node_id_2, text_2 = text_item_2['node_id'], text_item_2['node_str']
                similarity = self.get_similarity(text_1, text_2)
                if similarity >= self.target:
                    similar_list.append({
                        'text_1': text_1,
                        'node_id_1': node_id_1,
                        'text_2': text_2,
                        'node_id_2': node_id_2,
                        'similarity': similarity
                    })
        return similar_list

    def get_similarity(self, text1, text2):
        """
        计算两个文本之间的相似度
        """
        rat = SequenceMatcher(None, text1, text2).ratio()
        # print(f"{text1} * {text2} * {rat}")
        return rat


if __name__ == '__main__':
    # 示例文本列表
    texts = [
        "这是一段示例文本，用于测试相似度。",
        "这是一段示例文本，用于测试相似度。",
        "这是另一段不同的文本。",
        "这是一段示例文本，用于测试相似度差不多的情况。"
    ]

    # 找出相似文本对
    similar_pairs = FindSimilarTexts(texts, 0.8).main()

    # 输出结果
    for text1, text2, similarity in similar_pairs:
        print(f"文本 1: {text1}")
        print(f"文本 2: {text2}")
        print(f"相似度: {similarity * 100:.2f}%")
        print("-" * 50)
