# -*-encoding: utf-8 -*-
import re
import oss2
import requests
import urllib.parse
import datetime
import hmac
import hashlib
import base64

import constants
from app.basic.baseerror import ParamsError
from app.basic.log import logger


_placeholder = {}


class Storage(object):
    def __init__(self, host, access_id, access_secret):
        self.auth = oss2.Auth(access_id, access_secret)
        self.endpoint = host
        self.bucket_pool = {}

    @classmethod
    def clean_special_character(cls, s):
        return s.replace('?', '').replace('#', '').replace('%', '').replace('+', '')

    def get_bucket(self, bucket_name):
        if not self.bucket_pool.get(bucket_name):
            self.bucket_pool[bucket_name] = oss2.Bucket(self.auth, self.endpoint, bucket_name)
        return self.bucket_pool[bucket_name]

    def put_object_from_file(self, bucket_name, object_name, filename, headers=None):
        """
        上传一个文件到 oss
        :param bucket_name:
        :param object_name: 上传到 OSS 的路径
        :param filename: 本地文件路径
        :param headers:
        :return:
        """
        object_name = self.clean_special_character(object_name)
        bucket = self.get_bucket(bucket_name)
        return bucket.put_object_from_file(object_name, filename, headers=headers), object_name

    def put_object_from_string(self, bucket_name, object_name, obj, headers=None):
        object_name = self.clean_special_character(object_name)
        bucket = self.get_bucket(bucket_name)
        return bucket.put_object(object_name, obj, headers=headers), object_name

    def delete_object(self, bucket_name, object_name):
        """
        删除单个文件
        :param bucket_name: 存储桶名称
        :param object_name: 要删除的文件的路径 例如：files/abc.pdf
        :return:
        """
        bucket = self.get_bucket(bucket_name)
        bucket.delete_object(object_name)

    def batch_delete_objects(self, bucket_name, object_names):
        """
        批量删除多个文件
        :param bucket_name: 存储桶名称
        :param object_names: 要删除的文件的路径的集合 例如：['files/abc.pdf', 'files/cde.pdf']
        :return:
        """
        bucket = self.get_bucket(bucket_name)
        bucket.batch_delete_objects(object_names)

    def delete_object_by_prefix(self, bucket_name, prefix):
        """
        删除指定前缀或目录下的多个文件
        :param bucket_name: 存储桶名称
        :param prefix: 指定前缀或者目录，例如：src
                       如果您需要删除所有前缀为 src 的文件，则 prefix 设置为 src，prefix = 'src'
                       设置为 src 后，所有前缀为 src 的非目录文件、src 目录以及目录下的所有文件均会被删除。
                       如果您仅需要删除 src 目录及目录下的所有文件，则 prefix 设置为 src/。
                       prefix = 'src/'
        :return:
        """
        bucket = self.get_bucket(bucket_name)
        for obj in oss2.ObjectIterator(bucket, prefix=prefix):
            bucket.delete_object(obj.key)

    def copy_object_simple(self, bucket_name, source_key, target_key, **kwargs):
        """
        同 bucket 拷贝文件
        :param bucket_name:bucket 名称
        :param source_key:拷贝的来源路径，比如从 A 拷贝到 B，那么 source_key 就指 A，开头不需要 /
        :param target_key:拷贝的目标路径，比如从 A 拷贝到 B，那么 target_key 就指 B，开头不需要 /
        :return:
        """
        bucket = self.get_bucket(bucket_name)
        bucket.copy_object(bucket_name, source_key, target_key, **kwargs)

    def copy_object(self, target_bucket_name, source_bucket_name, source_key, target_key):
        """
        不同 bucket 拷贝文件
        :param target_bucket_name: 拷贝的目标 bucket，比如从 BA 拷贝到 BB，那么 bucket_name 就是 BB；
                                   target_bucket_name 需要鉴权
        :param source_bucket_name:拷贝的来源 bucket，比如从 BA 拷贝到 BB，那么 source_bucket_name 就是 BA
        :param source_key:拷贝的来源路径，比如从 A 拷贝到 B，那么 source_key 就指 A
        :param target_key:拷贝的目标路径，比如从 A 拷贝到 B，那么 target_key 就指 B
        :return:
        """
        bucket = self.get_bucket(target_bucket_name)
        bucket.copy_object(source_bucket_name, source_key, target_key)

    def object_exists_simple(self, bucket_name, key):
        """如果文件存在就返回True，否则返回False。如果Bucket不存在，或是发生其他错误，则抛出异常。"""
        key = self.clean_special_character(key)
        bucket = self.get_bucket(bucket_name)
        return bucket.object_exists(key)

    def put_symlink(self, bucket_name: str, target_key: str, symlink_key: str):
        """创建软连接
        只可创建在同一个 bucket 下
        target_key: 原始文件的 path
        symlink_key: 要生成的软链接文件的 path
        """
        bucket = self.get_bucket(bucket_name)
        res = bucket.put_symlink(target_key, symlink_key)
        return res

    def get_symlink(self, bucket_name: str, symlink_key: str):
        """获取软链接指向的目标文件名称
        symlink_key: 软链接的 path
        """
        bucket = self.get_bucket(bucket_name)
        return bucket.get_symlink(symlink_key).target_key

    def get_size(self, bucket_name: str, prefix: str):
        """
        获取文件大小
        :param bucket_name: bucket 名称
        :param prefix: 前缀 如果输入文件夹，则获取文件夹下所有文件的大小；如果输入为文件，则仅获取此文件大小
        :return:
        """
        bucket = self.get_bucket(bucket_name)
        res = []
        for obj in oss2.ObjectIterator(
                bucket,
                prefix=prefix,
                max_keys=1000
        ):
            size = obj.size / 1024
            size = f"{'%.2f' % (size / 1024)}MB" if size > 1024 else f"{'%.2f' % size}KB"
            res.append({
                'key': obj.key,
                'size': size
            })
        return res

    def put_object_acl(self, bucket_name: str, object_name: str, acl_type: int):
        """ 给文件对象设置权限
        文档：https://help.aliyun.com/document_detail/88455.html
        object_name: 例：work1/高优二轮仿真试卷数学文H.pdf
        acl_type:
            0: oss2.OBJECT_ACL_PRIVATE 私有
            1: oss2.OBJECT_ACL_PUBLIC_READ 公共读
            2: oss2.OBJECT_ACL_PUBLIC_READ_WRITE 公共读写
        """
        bucket = self.get_bucket(bucket_name)
        if acl_type == 0:
            acl = oss2.OBJECT_ACL_PRIVATE
        elif acl_type == 1:
            acl = oss2.OBJECT_ACL_PUBLIC_READ
        elif acl_type == 2:
            acl = oss2.OBJECT_ACL_PUBLIC_READ_WRITE
        else:
            raise ParamsError(f"权限类型设置不正确，请检查！")
        bucket.put_object_acl(object_name, acl)

    @classmethod
    def download_from_oss(cls, local_path, download_url):
        """
        从 OSS 下载文件
        :param local_path: 下载到本地的路径
        :param download_url: 将要下载的 oss 地址
        :return:
        """
        resp = requests.get(download_url, stream=True)
        if resp.status_code != 200:
            raise
        with open(local_path, 'wb') as f:
            for chunk in resp.iter_content(1024 * 1024 * 10):
                f.write(chunk)

    @classmethod
    def upload_to_oss(cls, local_path, upload_url):
        """
        把文件上传到 OSS
        :param local_path: 本地将要上传的文件的路径
        :param upload_url: 将要上传的 oss 地址
        :return:
        """
        upload_url = cls.clean_special_character(upload_url)
        with open(local_path, 'rb') as fp:
            data = fp.read()

        gmt_date = datetime.datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')

        ret = urllib.parse.urlparse(upload_url)
        netloc = ret.netloc
        bucket_name = re.search(r'(.*)\.(.*)\.aliyuncs\.com', netloc).group(1)
        path = ret.path

        b_access_key_secret = f'{constants.ACCESS_KEY_SECRET}'.encode('utf-8')
        b_msg = f"PUT\n\napplication/x-www-form-urlencoded\n{gmt_date}\n/{bucket_name}{path}".encode(
            'utf-8')

        h = hmac.new(b_access_key_secret, b_msg, hashlib.sha1)
        Signature = base64.b64encode(h.digest())
        Signature = Signature.decode('utf-8')
        Authorization = "OSS " + constants.ACCESS_KEY_ID + ":" + Signature

        retry_count = 3
        while retry_count:
            r = requests.put(
                upload_url,
                data=data,
                headers={
                    'Host': netloc,
                    'Authorization': Authorization,
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'Date': gmt_date
                })
            if r.status_code not in (200, 201):
                logger.info('oss put error, url:%s - code:%s - reason:%s' % (r.url, r.status_code, r.reason))
            else:
                break
            retry_count -= 1
        else:
            # 没有 break 证明失败了
            raise

    @classmethod
    def pre_url(cls, url: str):
        if constants.ENV == constants.Env.PRODUCT:
            url = url.replace(
                'oss-cn-shanghai.aliyuncs.com',
                'oss-cn-shanghai-internal.aliyuncs.com')
        return url

    def get_object_to_string(self, bucket_name, object_name):
        """ 获取 OSS 资源并返回字符串 """
        object_name = self.clean_special_character(object_name)
        bucket = self.get_bucket(bucket_name)
        resp = bucket.get_object(object_name)
        return resp.read().decode('utf-8')


storage = Storage(constants.OSS_HOST, constants.ACCESS_KEY_ID, constants.ACCESS_KEY_SECRET)


if __name__ == '__main__':
    ...
