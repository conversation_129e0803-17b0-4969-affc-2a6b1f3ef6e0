# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/5 14:32
import asyncio
import time
from enum import Enum

import httpx
from volcenginesdkarkruntime import Ark, AsyncArk

from constants import HUOSHA<PERSON>_AK
from app.basic.log import logger


class Doubao:
    @classmethod
    def chat(cls, prompt, model, temperature=1):
        client = Ark(
            api_key=HUOSHAN_AK,
            timeout=httpx.Timeout(timeout=1800)
        )
        completion = client.chat.completions.create(
            model=model,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                },
            ],
            temperature=temperature,
        )
        res = completion.choices[0].message.content
        total_tokens = completion.usage.total_tokens
        return res, total_tokens

    @classmethod
    async def async_chat(cls, prompt, model, temperature=1):
        # logger.info(f'async_v15_pro_32k start.')
        start_time = time.time()
        client = AsyncArk(
            api_key=HUOSHAN_AK,
            timeout=httpx.Timeout(timeout=1800)
        )
        completion = await client.chat.completions.create(
            model=model,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                },
            ],
            temperature=temperature,
        )
        res = completion.choices[0].message.content
        total_tokens = completion.usage.total_tokens
        # logger.info(f'async_v15_pro_32k success, cost_time={time.time() - start_time}s.')
        return res, total_tokens


class DBModel(Enum):
    LITE_32 = 'doubao-lite-32k-240828'
    V15_PRO_256 = 'doubao-1-5-pro-256k-250115'
    V15_PRO_32 = 'doubao-1-5-pro-32k-250115'
    DS_V3 = 'deepseek-v3-250324'
    DS_R1 = 'deepseek-r1-250120'


async def test():
   results = await asyncio.gather(
       Doubao.async_chat("1+1=?", DBModel.V15_PRO_32.value),
       Doubao.async_chat("1+1=?", DBModel.V15_PRO_256.value)
   )
   print(results)


if __name__ == '__main__':
    asyncio.run(test())
