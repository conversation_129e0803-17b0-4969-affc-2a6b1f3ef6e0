from app.basic.baseerror import InternalError
from app.basic.request import Request
from app.basic.log import logger
from constants import XDOC_HOST


class XdocApi(Request):
    base_url = XDOC_HOST

    @classmethod
    def get_data(cls, url, json: dict):
        if json['status'] == 0:
            return json.get('data')
        logger.error(f'''{url}：{json['status']} {json['message']}''')
        raise InternalError(json['message'], label='xdoc 系统')

    @classmethod
    def formula_check(cls, formulas: list):
        """ 公式检查 """
        check_list = cls.post(
            url='/api/open/formula/check',
            json={
                'formulas': formulas,
            },
            headers={'x-request-from': 'hexin'}
        )
        res = []  # value 约定，True 公式正常，False 公式不正常
        for index, item in enumerate(check_list):
            status = item['status']
            if status == 1:  # parse error
                is_correct = False
            else:
                is_correct = True

            res.append({
                'latex_str': formulas[index],
                'is_correct': is_correct,
            })
        return res

    @classmethod
    def html_to_json(cls, html_data: str):
        data = cls.post(
            url='/api/open/transfer/html2json',
            json={
                'html': html_data,
                'version': 'new'
            },
            headers={
               'x-request-from': 'hexin',
               'Content-Type': 'application/json'
            }
        )
        return data
