# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/23 15:08
import requests

from app.basic.log import logger
from constants import FILTER_TITLE_HOST
from app.basic.baseerror import InternalError


def filter_title(title_list):
    url = f'{FILTER_TITLE_HOST}/api/filter_title'

    retry = 3
    error_info = ''
    while retry:
        try:
            response = requests.post(
                url=url,
                json={'title_list': title_list}
            )
            if response.status_code != 200:
                error_info = f"{url} status code {response.status_code} title_list={title_list} response={response.text}"
                logger.info(error_info)
                continue
            data = response.json()
            if data['status'] != 'success':
                error_info = f"{url} request error, {data['data']} title_list={title_list} response={response.text}"
                logger.info(error_info)
                continue
            return data['data']
        except Exception as e:
            error_info = f"{url} request error, {e} title_list={title_list}"
            logger.info(error_info)
            retry -= 1
    else:
        raise InternalError(error_info)


if __name__ == '__main__':
    res = filter_title(['（2）本题考查词句理解与赏析。'])
    print(res)
