import ujson

from app.basic.baseerror import InternalError
from app.basic.request import Request
from app.basic.log import logger
from constants import QUEUE_HOST
from app.basic.util import time_util


class MQ(Request):
    base_url = QUEUE_HOST

    @classmethod
    def get_data(cls, url, json: dict):
        if json['code'] == 0:
            return json.get('data')
        logger.error(f'''{url}：{json['code']} {json['message']}''')
        raise InternalError(json['message'], label='队列管理系统')

    @classmethod
    def task_init(cls, task_id: str, task_type: str, task_info: dict):
        """队列初始化"""
        cls.post(
            url='/api/proxy/task/init',
            json={
                'task_id': task_id,
                'task_type': task_type,
                'task_info': task_info,
            }
        )

    @classmethod
    def get_task(cls, task_type: str):
        """获取任务"""
        url = '/api/proxy/task/get/info'
        task_info = cls.get(
            url=url,
            params={'task_type': task_type}
        )
        if time_util.is_output_log():
            logger.info(f"{url}?task_type={task_type} success")
        return task_info

    @classmethod
    def callback_task(cls, data: dict):
        """回调任务"""
        logger.info(f"回调 worker 的请求数据，{ujson.dumps(data, ensure_ascii=False)}")
        res = cls.post(
            url='/api/proxy/task/callback',
            json=data
        )
        return res

    @classmethod
    def get_task_count(cls, task_types: list, task_status: str = None):
        """队列信息查询"""
        res = cls.post(
            url='/api/proxy/task/count',
            json={
                'task_types': task_types,
                'task_status': task_status or 'init'
            }
        )
        return res
