# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/20 18:47
import time
import json

import aiohttp


async def async_request(model, prompt, temperature):
    url = "https://api.deepseek.com/chat/completions"
    payload = json.dumps({
        "messages": [
            {"content": prompt, "role": "user"}
        ],
        "model": model,
        "frequency_penalty": 0,
        "max_tokens": 2048,
        "presence_penalty": 0,
        "response_format": {
            "type": "text"
        },
        "stop": None,
        "stream": False,
        "stream_options": None,
        "temperature": temperature,
        "top_p": 1,
        "tools": None,
        "tool_choice": "none",
        "logprobs": False,
        "top_logprobs": None
    })
    headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': 'Bearer sk-93422fc9d0494b45916df9a7597cec77'
    }
    print(time.time())
    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, data=payload) as response:
            response_json = await response.json()
            result = response_json['choices'][0]['message']['content']
            cost_token = response_json['usage']['total_tokens']
            return result, cost_token


async def async_v3(prompt, temperature=1.0):
    print('>>>>>>>>>>>>>>>>>>>>>>>guanwang  async_v3')
    res = await async_request('deepseek-chat', prompt, temperature)
    return res


async def async_r1(prompt, temperature=1.0):
    res = await async_request('deepseek-reasoner', prompt, temperature)
    return res


async def test_async():
    await asyncio.gather(
        async_r1('你是谁？', 0.1),
        async_r1('1+1=?', 0.1),
    )


if __name__ == '__main__':
    import asyncio
    asyncio.run(test_async())
