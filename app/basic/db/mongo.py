from constants import MONGO_URI

from pymongo import mongo_client


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class MongoDB(metaclass=Singleton):
    mongo_client = None

    def __init__(self):
        self.mongo_client = mongo_client.MongoClient(
            MONGO_URI,
            maxPoolSize=10,
            connecttimeoutms=10000
        )


mongo_cli = MongoDB().mongo_client
