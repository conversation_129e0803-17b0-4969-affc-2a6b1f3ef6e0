import re
from bs4 import BeautifulSoup


def del_html_entities(s):
    html_entities = ["&nbsp;", "&lt;", "&gt;", "&amp;", "&quot;", "&apos;"]
    for e in html_entities:
        s = s.replace(e, "")
    return s


def del_html_tag(s):
    res = re.sub(r'<[^<>]*?>', '', s)
    return res


def split_html(s) -> list:
    res = re.split(r'\n', s)
    return res


def split_html_v2(html_content, is_add_line=False):
    """
    拆分 html
    """
    # 如果要加行号，则先把之前的行号清掉
    if is_add_line:
        html_content = re.sub(r' line="\d+"', '', html_content)
    soup = BeautifulSoup(html_content, 'html.parser')
    elements = []
    index = 0
    for tag in soup.find_all(['p', 'table', 'hr']):
        # 检查当前标签是否是 table 的子标签
        if tag.find_parent('table'):
            continue
        data = str(tag)
        # 添加 line 标签
        if is_add_line:
            pattern = re.compile(r'\s*(<p)|(<table)|(<hr)')
            if pattern.search(data):
                data = pattern.sub(lambda x: x.group() + f' line="{index}"', data, 1)
                index += 1
        elements.append(data)
    return elements


def join_html(html_list, is_del_line=True):
    data = '\n'.join(html_list)
    # 去掉 line 标签
    if is_del_line:
        data = re.sub(r'\s*line="\d+"', '', data)
    return data


def add_check_span(s):
    # if '###' in s:
    #     return s
    # return f'###{s}|||'

    if 'data-check="ai_check"' in s:
        return s
    return f'<span data-check="ai_check">{s}</span>'


def add_discard_span(s):
    if s:
        res = f'<span data-label="discard">{s}</span>'
    else:
        res = ''
    return res


def add_quest_num_span(s, level):
    return f'<span data-label="quest_num" data-level="{level}">{s}</span>'


def get_prefix_text(s):
    s = re.sub(r'<[^<>]*?>', '', s)
    s = re.sub(r'\$\$.*?\$\$', '', s)
    if len(s) > 10:
        return s[:10]
    else:
        return s


def get_serial_number(s, level=1):
    pattern = re.compile(rf'<span[^<>]*?data-label="quest_num" data-level="{level}"[^<>]*?>(.*?)</span>')
    if pattern.search(s):
        serial_number = pattern.search(s).group(1)
    else:
        serial_number = ''
    return serial_number


def filter_tags(input_text, allowed_tags):
    """
    1.过滤文本中的标签，只保留允许的标签
    2.删除空的 allowed_tags，例如 <sn1></sn1>
    参数:
    input_text (str): 输入的文本
    allowed_tags (list): 允许保留的标签列表

    返回:
    str: 过滤后的文本
    """
    # 构建允许的开始标签和结束标签的正则表达式
    allowed_start_tags = '|'.join([f'<{tag}>' for tag in allowed_tags])
    allowed_end_tags = '|'.join([f'</{tag}>' for tag in allowed_tags])

    # 构建允许的标签模式
    allowed_pattern = f'({allowed_start_tags}|{allowed_end_tags})'

    # 查找所有标签
    all_tags = re.findall(r'<[^>]*>', input_text)

    # 过滤掉不在允许列表中的标签
    filtered_text = input_text
    for tag in all_tags:
        if not re.match(allowed_pattern, tag):
            filtered_text = filtered_text.replace(tag, '')

    # 删除空标签
    blank_tags = '|'.join([f'(<{tag}></{tag}>)' for tag in allowed_tags])
    filtered_text = re.sub(blank_tags, '', filtered_text)
    return filtered_text
