# -*-encoding: utf-8 -*-

import base64
import copy
import datetime
import functools
import hashlib
import math
import re
import time
import typing
import urllib.error
import urllib.parse
import urllib.request
import uuid
import zlib
import json
import ujson
from collections import defaultdict
from fractions import Fraction
from bson import ObjectId
import traceback
import requests
import os

from app.basic.baseerror import ParamsError


pattern = re.compile(r'^(192|172|10|100|127)\.')
RE_PROP_NAME = r'''[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))'''
RE_ESCAPE_CHAR = r'\\(\\)?'

TIME_FORMAT_DATE = '%Y-%m-%d'
TIME_FORMAT_DATETIME = '%Y-%m-%d %H:%M:%S'
TIME_FORMAT_UTC = '%Y-%m-%dT%H:%M:%S.%fZ'


class IterItem:
    def __init__(
        self,
        *,
        node,
        index: int,
        level: int,
        parent,
        siblings: list,
        path: list,
        stop_iter_subs=lambda: None,
    ):
        self.node = node
        self.index = index
        self.level = level
        self.parent = parent
        self.siblings = siblings
        self.path = path
        self.stop_iter_subs = stop_iter_subs


class JSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime.datetime):
            return obj.strftime(TIME_FORMAT_DATETIME)
        elif isinstance(obj, ObjectId):
            return str(obj)
        else:
            return json.JSONEncoder.default(self, obj)


def generate_hash_uuid(limit=None):
    u = uuid.uuid4().hex
    if limit:
        u = u[:limit]
    return u


def dct_sorted(d):
    # 根据字典键的升序排序
    sorted(d.items(), key=lambda x: x[0])


def get_datetime_now():
    return datetime.datetime.now()


def get_std_timestamp():
    return get_datetime_now().strftime('%Y%m%d%H%M%S%f')[:-3]


def get_dates_until_today():
    # 返回一个数组，其下日期格式为：2024-10-12
    today = datetime.datetime.now()
    start_date = datetime.datetime(today.year, today.month, 1)
    dates = []
    current_date = start_date
    while current_date <= today:
        dates.append(current_date.strftime('%Y-%m-%d'))
        current_date += timedelta(days=1)
    return dates


def get_dates_between(start_date_str, end_date_str):
    # 时间格式：2024-10-12
    start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d')
    end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d')
    dates = []
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime('%Y-%m-%d'))
        current_date += timedelta(days=1)
    return dates


def get_previous_day(date_str):
    # 时间格式：2024-10-12
    # 获取前一天的时间，如果遇到周日，则再自动提前一天
    date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    previous_day = date - datetime.timedelta(days=1)
    weekday = previous_day.weekday()
    if weekday == 6:  # Sunday
        previous_day = previous_day - datetime.timedelta(days=1)
    return previous_day.strftime('%Y-%m-%d')


def get_dates_before(date_str, n):
    # 查询 data_str 到 n 天前的所有时间的数组
    # 时间格式：2024-10-12
    date = datetime.datetime.strptime(date_str, '%Y-%m-%d')
    dates = []
    for i in range(1, n + 1):
        prev_date = date - timedelta(days=i)
        dates.append(prev_date.strftime('%Y-%m-%d'))

    return dates

def get_timestamp():
    return time.time()


def time_create(t, f=TIME_FORMAT_DATETIME):
    return t.strftime(f)


def strptime(t, f=TIME_FORMAT_DATETIME):
    return datetime.datetime.strptime(t, f)


def get_datetime_utcnow():
    return datetime.datetime.utcnow()


def from_timestamp(timestamp):
    return datetime.datetime.fromtimestamp(timestamp)


def to_timestamp(t):
    return time.mktime(t.timetuple())


def str2datetime(s, category: str):
    """
    字符串时间转 DATETIME 类型
    :param s: 字符串时间
    :param category: 字符串时间的类型
           day:%Y-%m-%d  hour:%Y-%m-%d %H:%M:%S  utc:%Y-%m-%dT%H:%M:%S.%fZ
    :return:
    """
    if category == 'day':
        _format = TIME_FORMAT_DATE
    elif category == 'hour':
        _format = TIME_FORMAT_DATETIME
    elif category == 'utc':
        _format = TIME_FORMAT_UTC
    else:
        raise ParamsError('category 传参不正确，请确认！')
    return datetime.datetime.strptime(s, _format)


def utc2local(utc_str):
    utc_st = datetime.datetime.strptime(utc_str, TIME_FORMAT_UTC)
    local_time = datetime.datetime.fromtimestamp(time.time())
    utc_time = datetime.datetime.utcfromtimestamp(time.time())
    time_difference = local_time - utc_time
    local_st = utc_st + time_difference
    return local_st.strftime(TIME_FORMAT_DATETIME)


def local2utc(local_str):
    local_st = datetime.datetime.strptime(local_str, TIME_FORMAT_DATETIME)
    time_struct = time.mktime(local_st.timetuple())
    utc_st = datetime.datetime.utcfromtimestamp(time_struct)
    return utc_st.strftime(TIME_FORMAT_UTC)


def round(n, d=0):
    if d:
        f = 10 ** d
        return int(n * f + 0.5) / f
    return int(n + 0.5)


def sha1(string):
    return hashlib.sha1(string.encode('UTF-8') if isinstance(string, str) else string).hexdigest()


def b64encode(bytestring):
    return base64.b64encode(bytestring.encode('UTF-8') if isinstance(bytestring, str) else bytestring)


def b64decode(bytestring):
    return base64.b64decode(bytestring.encode('UTF-8') if isinstance(bytestring, str) else bytestring)


def dumps(obj, compress=False, ensure_ascii=True):
    if compress:
        compress_obj = zlib.compressobj()
        compress_obj = compress_obj.compress(ujson.dumps(obj).encode('utf-8')) + compress_obj.flush()
        return base64.b64encode(compress_obj).decode('utf-8')
    else:
        return json.dumps(obj, ensure_ascii=ensure_ascii, cls=JSONEncoder)


def loads(string, default=None, decompress=False):
    if not string:
        return default
    if decompress:
        decompress_obj = zlib.decompressobj()
        decompress_obj = decompress_obj.decompress(
            base64.b64decode(string.encode('utf-8'))) + decompress_obj.flush()
        return ujson.loads(decompress_obj)
    else:
        return ujson.loads(string)


def timedelta(**kwargs):
    return datetime.timedelta(**kwargs)


def urlparse(url):
    return urllib.parse.urlparse(url)


def urlencode(data, **kwargs):
    return urllib.parse.urlencode(data, **kwargs)


def urljoin(host, url):
    return urllib.parse.urljoin(host, url)


def get_url_path(url):
    path = urlparse(url).path
    if path.startswith('/'):
        path = path[1:]
    return path


def group_by(iterable, key, value=lambda i: i):
    groups = defaultdict(list)
    for item in iterable:
        groups[key(item)].append(value(item))
    return groups


def chunk(iterable, size):
    """
    for batch, start_index in chunk(range(10), 3):
        print(batch, start_index)
    """
    arr = list(iterable)
    total = len(arr)
    for i in range((total + size - 1) // size):
        start, end = i * size, min((i + 1) * size, total)
        yield arr[start: end], start


def chunk_v2(lst: list, size: int):
    output_list = [lst[i:i + size] for i in range(0, len(lst), size)]
    return output_list


def merge(*args):
    if not args:
        return
    args = copy.deepcopy(args)
    if len(args) == 1:
        return args[0]

    def _merge(a, b):
        if not isinstance(a, dict) or not isinstance(b, dict):
            return b
        r = {}
        for k in a.keys() | b.keys():
            if k not in a:
                r[k] = b[k]
            elif k not in b:
                r[k] = a[k]
            else:
                r[k] = _merge(a[k], b[k])
        return r

    res, *rest = args
    for arg in rest:
        res = _merge(res, arg)
    return res


def iter_tree(
    nodes,
    level=0, parent=None, path=False, order='pre',
    sub_prop=None, sub_attr=None,
    get_subs=lambda n: n.get('children'),
):
    for index, node in enumerate(nodes):
        stop = False
        if path is True:
            p = [node]
        elif isinstance(path, list):
            p = path + [node]
        else:
            p = None
        item = IterItem(node=node, index=index, level=level, parent=parent, siblings=nodes, path=p)
        if order == 'pre':
            def stop_iter_subs():
                nonlocal stop
                stop = True

            item.stop_iter_subs = stop_iter_subs
            yield item
        if not stop:
            if sub_prop:
                children = node.get(sub_prop)
            elif sub_attr:
                children = getattr(node, sub_attr, None)
            else:
                children = get_subs(node)
            if children:
                yield from iter_tree(
                    children,
                    level=level + 1,
                    parent=node,
                    order=order,
                    path=p,
                    sub_prop=sub_prop,
                    sub_attr=sub_attr,
                    get_subs=get_subs,
                )
        if order == 'post':
            yield item


def iter_find_tree(
    nodes, predictor,
    stop_iter_subs=True, level=0, parent=None, path=False, order='pre',
    sub_prop=None, sub_attr=None,
    get_subs=lambda n: n.get('children'),
):
    for item in iter_tree(
        nodes,
        level=level,
        parent=parent,
        path=path,
        order=order,
        sub_prop=sub_prop,
        sub_attr=sub_attr,
        get_subs=get_subs,
    ):
        if predictor(item):
            yield item.node
            if stop_iter_subs and item.stop_iter_subs:
                item.stop_iter_subs()


def find_tree(
    nodes, predictor,
    level=0, parent=None, path=False, order='pre',
    sub_prop=None, sub_attr=None,
    get_subs=lambda n: n.get('children'),
):
    for item in iter_tree(
        nodes,
        level=level,
        parent=parent,
        path=path,
        order=order,
        sub_prop=sub_prop,
        sub_attr=sub_attr,
        get_subs=get_subs,
    ):
        if predictor(item):
            return item.node


def string_to_path(prop):
    result = []

    def helper(g):
        number, quote, string = g.groups()
        if quote:
            result.append(re.sub(RE_ESCAPE_CHAR, '$1', string))
        elif number:
            result.append(int(number))
        else:
            result.append(g.group())

    re.sub(RE_PROP_NAME, helper, prop)
    return result


def parse_getter(prop):
    keys = prop if isinstance(prop, (list, tuple)) else string_to_path(prop)

    def wrapper(obj, strict=False, default=None):
        for key in keys:
            if not obj and not strict:
                return default
            if isinstance(obj, dict) and key in obj:
                obj = obj[key]
            elif isinstance(obj, (list, tuple)) and key < len(obj):
                obj = obj[key]
            elif strict:
                raise KeyError(key)
            else:
                return default
        return obj

    return wrapper


def get_val(obj, prop, strict=False, default=None):
    """读取值"""
    return parse_getter(prop)(obj, strict, default)


def parse_setter(prop):
    keys = prop if isinstance(prop, (list, tuple)) else string_to_path(prop)

    def set_attr(obj, key, val):
        if isinstance(obj, list) and key >= len(obj):
            obj.extend([None] * (key - len(obj) + 1))
        obj[key] = val

    def wrapper(obj, val):
        origin = obj
        for i, key in enumerate(keys):
            is_last = i == len(keys) - 1
            if key not in obj:
                next_key = None if is_last else keys[i + 1]
                set_attr(obj, key, [] if isinstance(next_key, int) else {})
            if is_last:
                set_attr(obj, key, val)
            else:
                obj = obj[key]
        return origin

    return wrapper


def set_val(obj, prop, val):
    """设置值"""
    return parse_setter(prop)(obj, val)


# 转换时标记为删除
cast_delete = {}


def cast_key(obj, convert: typing.Callable):
    if isinstance(obj, dict):
        obj = {convert(key): cast_key(value, convert) for key, value in obj.items()}
        return {key: value for key, value in obj.items() if key is not cast_delete}
    if isinstance(obj, list):
        return [cast_key(value, convert) for value in obj]
    if isinstance(obj, tuple):
        return tuple(cast_key(value, convert) for value in obj)
    return obj


def cast_value(obj, convert: typing.Callable):
    if isinstance(obj, dict):
        obj = {key: cast_value(value, convert) for key, value in obj.items()}
        return {key: value for key, value in obj.items() if value is not cast_delete}
    if isinstance(obj, list):
        return [cast_value(value, convert) for value in obj]
    if isinstance(obj, tuple):
        return tuple(cast_value(value, convert) for value in obj)
    return convert(obj)


@functools.lru_cache(maxsize=512)
def str_snake_to_camel(s: str, capitalize: bool = False) -> str:
    """do_something => doSomething"""
    if not s:
        return s
    s = re.sub(r'_(.)', lambda g: g.group(1).upper(), s)
    if capitalize:
        s = s[0].upper() + s[1:]
    return s


@functools.lru_cache(maxsize=512)
def str_camel_to_snake(s: str) -> str:
    """doSomething => do_something"""
    if not s:
        return s
    s = re.sub(r'^[A-Z]+', lambda g: g.group(0).lower(), s)
    s = re.sub(r'[A-Z]+', lambda g: f'_{g.group(0).lower()}', s)
    return s


def snake_to_camel(obj, capitalize=False):
    def convert(key):
        return str_snake_to_camel(key, capitalize)

    return cast_key(obj, convert)


def camel_to_snake(obj):
    return cast_key(obj, str_camel_to_snake)


def del_empty_str_in_list(lst: list):
    return [one for one in lst if one is not None and one not in ('', '\n')]


def decimal2fraction(decimal, precision: int = None, max_denominator: int = None):
    if not decimal:
        return None
    f, i = math.modf(decimal)
    i = int(i)
    if precision:
        f = str(round(f * precision) / precision)
    fra = Fraction(f).limit_denominator(max_denominator or 1000000)
    return fra.numerator, fra.denominator, i


def parse_color(c: (str, list)) -> list:
    if not c:
        return
    rgb = []
    if isinstance(c, str):
        c = re.sub(r'\s', '', c)
        m = re.match(r'^rgba?\((\d+),(\d+),(\d+)(,(\d+))?\)$', c, re.IGNORECASE)
        if m:
            rgb = [int(m.group(1)), int(m.group(2)), int(m.group(3))]
        m = re.match(r'^#?([\da-f]{6})$', c, re.IGNORECASE)
        if m:
            g = m.group(1)
            rgb = [int(g[:2], 16), int(g[2:4], 16), int(g[-2:], 16)]
        m = re.match(r'^#?([\da-f]{3})$', c, re.IGNORECASE)
        if m:
            g = m.group(1)
            rgb = [int(g[0] * 2, 16), int(g[1] * 2, 16), int(g[2] * 2, 16)]
    elif isinstance(c, list):
        rgb = c
    return rgb2cmyk(rgb)


def rgb2cmyk(rgb: list) -> list:
    cmyk_scale = 100
    r, g, b = rgb
    if (r == 0) and (g == 0) and (b == 0):
        # black
        return 0, 0, 0, cmyk_scale

    # rgb [0,255] -> cmy [0,1]
    c = 1 - r / 255.
    m = 1 - g / 255.
    y = 1 - b / 255.

    # extract out k [0,1]
    min_cmy = min(c, m, y)
    c = (c - min_cmy) / (1 - min_cmy)
    m = (m - min_cmy) / (1 - min_cmy)
    y = (y - min_cmy) / (1 - min_cmy)
    k = min_cmy

    # rescale to the range [0,cmyk_scale]
    return [int(i * cmyk_scale) for i in (c, m, y, k)]


def find_and_replace_use_index(s: str, sub: str, repl: str, nth: int):
    find = s.find(sub)
    if find == -1:
        return s
    i = 1  # 与nth对标
    while find != -1 and i != nth:
        find = s.find(sub, find + 1)
        i += 1
    return s[:find] + repl + s[find + len(sub):]


def get_file_md5(file_path):
    m = hashlib.md5()
    with open(file_path, 'rb') as fp:
        data = fp.read()
        if not data:
            raise ValueError(f"读取不到文件内容，请检查！file path: {file_path}")
        m.update(data)
        return m.hexdigest()


def get_str_md5(input_string: str) -> str:
    """计算字符串的 MD5 哈希值"""
    # 将字符串编码为 bytes（MD5 算法需要 bytes 类型输入）
    encoded_string = input_string.encode('utf-8')

    # 创建 MD5 哈希对象
    md5_hash = hashlib.md5()

    # 更新哈希对象的内容
    md5_hash.update(encoded_string)

    # 获取十六进制表示的哈希值
    return md5_hash.hexdigest()


def format_error():
    return traceback.format_exc().replace('\n', '; ')


def check_url(url):
    response = requests.get(url)
    if response.status_code == 200:
        return True
    return False


def get_file_name(url):
    res = os.path.split(url)[1].split('?')[0]
    return res


def chinese_chars_count(s):
    # 初始化中文字符的计数器
    cn_count = 0
    for char in s:
        if '\u4e00' <= char <= '\u9fff':
            cn_count = cn_count + 1

    return cn_count


def count_chinese_english_chars(s):
    # 初始化中文字符和英文字符的计数器
    cn_count = 0
    en_count = 0
    cn_char_set = set()
    for char in s:
        if '\u4e00' <= char <= '\u9fff':
            cn_count = cn_count + 1
            cn_char_set.add(char)
        elif 'a' <= char.lower() <= 'z':
            en_count = en_count + 1

    return cn_count, en_count


class Dict(dict):
    def __getattr__(self, item):
        resp = self.get(item, None)
        if isinstance(resp, dict):
            resp = Dict(resp)
        return resp

    def __setattr__(self, key, value):
        self[key] = value

    def __deepcopy__(self, memodict={}):
        return Dict((copy.deepcopy(k, memodict), copy.deepcopy(v, memodict)) for k, v in self.items())

    def __copy__(self):
        return Dict(self.items())


if __name__ == '__main__':
    print(get_str_md5('\\mathit{VEGFA}'))
