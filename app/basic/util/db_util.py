# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/19 20:38
from app.basic.db.mongo import mongo_cli
from app.basic.util import utils

from bson import ObjectId


def html_fix_task_create(task_id, machine_url, subject, round_count, tag):
    """
    创建 html fix 信息数据
    """
    # 如果库里有 task，则先删后创建
    res = mongo_cli['ai-util']['html_fix_task'].find_one({'task_id': task_id})
    if res:
        mongo_cli['ai-util']['html_fix_task'].delete_many({'task_id': task_id})
        mongo_cli['ai-util']['html_fix_log'].delete_many({'task_id': task_id})

    datetime_now = utils.get_datetime_now()
    res = mongo_cli['ai-util']['html_fix_task'].insert_one({
        'task_id': task_id,
        'machine_url': machine_url,
        'subject': subject,
        'round_count': round_count,
        'tag': tag,
        'available': True,
        'create_time': datetime_now,
    })
    return str(res.inserted_id)


def html_fix_task_update(_id, stat):
    """
    更新 html fix 信息数据
    """
    mongo_cli['ai-util']['html_fix_task'].update_one(
        filter={'_id': ObjectId(_id)},
        update={'$set': {'stat': stat}}
    )


def html_fix_log_create(parent_id, task_id, subject, round_index, error_info, tag):
    """
    创建 html fix log 日志
    """
    datetime_now = utils.get_datetime_now()
    mongo_cli['ai-util']['html_fix_log'].insert_one({
        'parent_id': parent_id,
        'task_id': task_id,
        'subject': subject,
        'round_index': round_index,
        'error_info': error_info,
        'available': True,
        'create_time': datetime_now,
        'tag': tag,
    })


def is_run_xdoc_html_fix():
    result = mongo_cli['ai-util']['config'].find_one(filter={
        'config_type': 'is_run_xdoc_html_fix'
    })
    return result['is_run']
