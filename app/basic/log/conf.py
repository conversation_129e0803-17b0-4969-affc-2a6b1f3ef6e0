import os
from constants import ENV, BASE_DIR, LOG_DIR, LOG_MAX_BYTES, LOG_BACKUP_COUNT, PROJECT_NAME


if ENV == 'stable':
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR)
else:
    LOG_DIR = os.path.join(BASE_DIR, 'temp_log')
    if not os.path.exists(LOG_DIR):
        os.makedirs(LOG_DIR)

LOG_FILE_PATH = os.path.join(LOG_DIR, f'{PROJECT_NAME}.log')
if not os.path.exists(LOG_FILE_PATH):  # 不存在则创建
    with open(LOG_FILE_PATH, 'w+') as f:
        f.close()

LOGGING = {
    # 基本设置
    'version': 1,  # 日志级别
    'disable_existing_loggers': False,  # 是否禁用现有的记录器

    # 日志格式集合
    'formatters': {
        # 标准输出格式
        'standard': {
            'format': '%(asctime)s %(message)s'
        },
        'precise': {
            'format': '%(asctime)s %(message)s'
        },
    },

    # 处理器集合
    'handlers': {
        'default': {
            'level': 'DEBUG',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': LOG_FILE_PATH,  # 日志输出文件
            'when': 'D',
            'interval': 1,
            'backupCount': 7,
            'encoding': 'utf-8',
            'formatter': 'standard',  # 使用哪种formatters日志格式
        },
        'error': {
            'level': 'ERROR',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': LOG_FILE_PATH,  # 日志输出文件
            'when': 'D',
            'interval': 1,
            'backupCount': LOG_BACKUP_COUNT,
            'encoding': 'utf-8',
            'formatter': 'standard',
        },
        # 输出到控制台
        'console': {
            'level': 'DEBUG',  # 输出信息的最低级别
            'class': 'logging.StreamHandler',
            'formatter': 'precise',
        },
        'request_handler': {
            'level': 'DEBUG',
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': LOG_FILE_PATH,  # 日志输出文件
            'when': 'D',
            'interval': 1,
            'backupCount': LOG_BACKUP_COUNT,
            'encoding': 'utf-8',
            'formatter': 'standard',
        },
    },

    # 日志管理器集合
    'loggers': {
        # 管理器
        'tornado': {
            'handlers': ['default', 'console'],
            'level': 'INFO',
            'propagate': False
        },
        'tornado.request': {
            'handlers': ['request_handler'],
            'level': 'INFO',
            'propagate': False,
        },
        'tornado.error': {
            'handlers': ['default', 'error'],
            'level': 'ERROR',
            'propagate': True
        }
    }
}

import logging.config

# 加载前面的标准配置
logging.config.dictConfig(LOGGING)

import logging

# 解决 pdfminer 输出大量 info 日志
# answer from https://github.com/jsvine/pdfplumber/discussions/529
logging.getLogger("pdfminer").setLevel(logging.WARNING)

# 获取loggers其中的一个日志管理器
logger = logging.getLogger("tornado")
