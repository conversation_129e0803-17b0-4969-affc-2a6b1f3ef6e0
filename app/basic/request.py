import time

import ujson
from urllib.parse import urljoin

import requests

from app.basic.log import logger
from app.basic.baseerror import BaseError
from app.basic.util import utils


class Request:
    base_url = ''

    @classmethod
    def _get_res(cls, url, resp, data_type):
        if resp.status_code not in (200, 201):
            text = resp.text
            logger.error(f'{url}: {resp.status_code} {text}')
            raise BaseError(f'{url}：{resp.status_code} 网络错误!')
        if data_type == 'text':
            return resp.text
        elif data_type == 'json':
            return ujson.loads(resp.text)
        else:
            return resp.content.read()

    @classmethod
    def get_data(cls, url: str, json: object):
        return json

    @classmethod
    def get_params(cls, params):
        return params

    @classmethod
    def get(cls, url: str, params: object = None, content_type: str = 'application/json',
            data_type='json', **kwargs):
        if 'http' not in url:
            url = urljoin(cls.base_url, url)
        if content_type:
            headers = kwargs.get('headers') or {}
            headers['Content-Type'] = content_type
            kwargs['headers'] = headers

        params = cls.get_params(params) if params else None
        resp = requests.get(url=url, params=params, **kwargs)
        data = cls._get_res(url, resp, data_type)
        return cls.get_data(url, data)

    @classmethod
    def post(cls, url: str, json: object = None, content_type: str = 'application/json', data_type='json', **kwargs):
        # logger.info(f"Start request {url}.")
        start_time = int(time.time())
        if 'http' not in url:
            url = cls.base_url + url
        if content_type:
            headers = kwargs.get('headers') or {}
            headers['Content-Type'] = content_type
            kwargs['headers'] = headers

        retry = 3
        while retry:
            try:
                req_data = utils.dumps(cls.get_params(json) if json else json)
                # logger.info(f"Request data={req_data}")
                resp = requests.post(url=url, data=req_data, **kwargs)
                # logger.info(f"{resp.status_code} ** {resp.text}")
                if resp.status_code != 200:
                    retry -= 1
                else:
                    data = cls._get_res(url, resp, data_type)
                    # logger.info(f"{url} cost_time={int(time.time()) - start_time}")
                    res = cls.get_data(url, data)
                    # logger.info(f"请求外部 api，url={url} request_data={req_data} response_data={res}")
                    return res
            except Exception as e:
                retry -= 1

    @classmethod
    def put(cls, url: str, json: object = None, content_type: str = 'application/json', data_type='json', **kwargs):
        if 'http' not in url:
            url = cls.base_url + url
        if content_type:
            headers = kwargs.get('headers') or {}
            headers['Content-Type'] = content_type
            kwargs['headers'] = headers

        req_data = utils.dumps(cls.get_params(json) if json else json)
        resp = requests.put(url=url, data=req_data, **kwargs)
        data = cls._get_res(url, resp, data_type)
        return cls.get_data(url, data)
