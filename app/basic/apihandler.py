# -*-encoding: utf-8 -*-

import time
import re
import ujson
import datetime
import inspect
from bson.objectid import ObjectId

import tornado.web
from tornado.escape import json_decode

from app.basic.log import logger
from app.basic.util import utils
from app.basic.baseerror import <PERSON><PERSON>rror, Params<PERSON>rror, AuthError


class APIHandler(tornado.web.RequestHandler):
    query_or_body: utils.Dict = None
    user = utils.Dict()

    _is_lan: bool = None
    _response_code = None

    async def response(self, handler, *args, **kwargs):
        data = None
        self.set_code(0)
        try:
            start_time = time.time()
            res = handler(*args, **kwargs)
            data = (await res) if inspect.iscoroutine(res) else res
            data = self.convert_data(data)
            delta = time.time() - start_time
            message = f'{int(delta * 1000)}ms'
        except (AuthError, ParamsError) as e:
            self.set_code(e.code)
            message = str(e)
            self.log('INFO', message)
        except BaseError as e:
            self.set_code(e.code)
            message = str(e)
            self.log('ERROR', message, exc_info=True)
        except tornado.web.HTTPError as e:
            self.set_code(1)
            message = str(e)
            self.set_status(e.status_code, e.reason)
        except Exception as e:
            self.set_code(1)
            message = str(e)
            self.log('ERROR', message, exc_info=True)
        self.end(data, message)

    def set_code(self, code):
        self._response_code = code

    def get_code(self):
        return self._response_code

    @classmethod
    def convert_data(cls, data):
        def convert_value(v):
            if isinstance(v, datetime.datetime):
                return v.strftime(utils.TIME_FORMAT_DATETIME)
            if isinstance(v, datetime.date):
                return v.strftime(utils.TIME_FORMAT_DATE)
            if isinstance(v, float):
                return round(v, 6)
            if isinstance(v, ObjectId):  # 去掉 ObjectId
                return utils.cast_delete
            return v

        data = utils.cast_value(data, convert_value)
        return data

    def log(self, level, msg, *args, **kwargs):
        try:
            level = level.lower()
            if level in ('debug', 'info', 'warning', 'error', 'critical'):
                func = getattr(logger, level)
                message = ''.join([
                    self._request_summary(),
                    self._request_query_summary(),
                    '\n  ',
                    msg,
                ])
                func(message, *args, **kwargs)
        except Exception as e:
            print(e)

    def _request_summary(self):
        summary = [
            f'({self.get_code()})',
            self.request.method,
            self.request.uri,
            f'({self.request.remote_ip})',
        ]
        if self.is_lan:  # 局域网
            summary.append('(lan)')
        if self.user.id:
            summary.append(f'user_id={self.user.id}')
        return ' '.join(str(i) for i in summary)

    def _request_query_summary(self):
        if not self.query_or_body:
            return ''
        query = f'{self.query_or_body}'
        limit = 250
        query = query[:limit - 3] + '...}' if len(query) > limit else query
        return f'\n  input={query}'

    @property
    def is_lan(self):
        """请求是否来源局域网"""
        if self._is_lan is None:
            self._is_lan = bool(re.match(r'^(172|192|10|127|100)\.', self.request.remote_ip))
        return self._is_lan

    def end(self, data, message):
        res = {'code': self.get_code(), 'message': message, 'data': data}
        self.set_header("Content-Type", "application/json; charset=UTF-8")
        self.set_default_headers()
        self.write(ujson.dumps(res))

    def set_default_headers(self):
        # 设置响应头
        self.set_header('Access-Control-Allow-Origin', '*')
        # self.set_header("Access-Control-Allow-Credentials", "true")
        self.set_header('Access-Control-Allow-Headers', 'X-Requested-With, Content-Type, Authorization')
        self.set_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS, DELETE, PUT, PATCH')
        self.set_header('Access-Control-Expose-Headers', 'Content-Disposition')  # 新增暴露自定义头

    def options(self):
        self.set_status(204)
        self.finish()

    @property
    def input(self):
        if self.query_or_body is None:
            query_or_body = None
            if self.request.query_arguments:
                query_or_body = {k: v[0].decode() for k, v in self.request.query_arguments.items()}
            elif self.request.body_arguments:
                query_or_body = {k: v[0].decode() for k, v in self.request.body_arguments.items()}
            elif self.request.body:
                query_or_body = json_decode(self.request.body)
            self.query_or_body = utils.Dict(query_or_body) if query_or_body else utils.Dict()
        return self.query_or_body

    def get_cookie_dict(self):
        return {k: v.value for k, v in self.cookies.items()}

    async def get(self, *args, **kwargs):
        await self.response(self.get_handler, *args, **kwargs)

    async def post(self, *args, **kwargs):
        await self.response(self.post_handler, *args, **kwargs)

    async def put(self, *args, **kwargs):
        await self.response(self.put_handler, *args, **kwargs)

    async def patch(self, *args, **kwargs):
        await self.response(self.patch_handler, *args, **kwargs)

    async def delete(self, *args, **kwargs):
        await self.response(self.delete_handler, *args, **kwargs)

    def _unimplemented_method(self, *args: str, **kwargs: str):
        raise tornado.web.HTTPError(405)

    get_handler = _unimplemented_method
    post_handler = _unimplemented_method
    put_handler = _unimplemented_method
    patch_handler = _unimplemented_method
    delete_handler = _unimplemented_method

    async def data_received(self, chunk: bytes):
        pass


class APIAttachmentHandler(APIHandler):
    @classmethod
    def convert_data(cls, data):
        return data

    def end(self, data, message):
        if not data:
            super(APIAttachmentHandler, self).end(data, message)
        else:
            body, filename = data
            self.set_header('Content-Type', 'application/octet-stream')
            self.set_header('Content-Disposition', f'attachment; filename={filename}')

            self.finish(body)
