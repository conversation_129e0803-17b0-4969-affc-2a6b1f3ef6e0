
_placeholder = {}


class BaseError(Exception):
    code = 1
    label = None

    def __init__(self, message='', code=None, label=None):
        if label is None:
            label = _placeholder
        if code:
            self.code = code
        if label is not _placeholder:
            self.label = label

        self.message = message

    def __str__(self):
        if self.message:
            self.message = f": {self.message}"
        return f'{self.label}{self.message}' if self.label else self.message


class VerifyError(BaseError):
    # 可用性错误
    code = 1001
    label = 'VerifyError'


class LogicError(BaseError):
    # 逻辑错误
    code = 1002
    label = 'LogicError'


class ParamsError(BaseError):
    # 参数错误
    code = 1003
    label = 'ParamsError'


class InternalError(BaseError):
    # 服务错误
    code = 1004
    label = 'InternalError'


class CallbackError(BaseError):
    # 回调异常
    code = 1005
    label = 'CallbackError'


class DataDoesNotExistError(BaseError):
    # 获取数据不存在异常
    code = 1006
    label = 'DataDoesNotExistError'


class ModelResultError(BaseError):
    # 模型返回结果异常
    code = 1006
    label = 'ModelResultError'


class AuthError(BaseError):
    # 认证异常
    code = 1007
    label = 'AuthError'


class TokenTooMuchError(BaseError):
    # Token 花费过多
    code = 1008
    label = 'TokenTooMuchError'
