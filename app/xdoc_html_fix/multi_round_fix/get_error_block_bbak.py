# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:31
import copy
import json
import re

from app.basic.api.xdoc import XdocApi
from app.basic.util import utils
from app.enums.node import QuestionType, ERROR_INFO_WHITE_LIST
from app.basic.util import html_util


class GetErrorBlock:

    def __init__(self, html_list: list, round_index: int):
        self.html_list = html_list
        self.round_index = round_index
        self.html_data = html_util.join_html(self.html_list, is_del_line=False)

        # 记录上一次跑过的题目
        self.pred_error_block_question = []

    def main(self):
        """ 获取 checker 结果 """
        node_list = self.get_node_list()
        # 获取失败的 block
        sub_html_list = []
        for index, node in enumerate(node_list):
            if not self.has_error(node):
                continue
            block_list = self.get_block(node, node_list, index)
            sub_html_list += block_list

        # 收集所有的已经跑过的题目前缀
        line_list = []
        for item in sub_html_list:
            prefix_text = item['prefix_text']
            line_list.append(prefix_text)
        return sub_html_list, line_list

    def get_node_list(self):
        """
        使用 xdoc 接口 html 转 json
        把 json 打平
        """
        result = XdocApi.html_to_json(self.html_data)
        temp = json.dumps(result)
        # 打平树
        node_list = []
        for item in utils.iter_tree(result):
            node = copy.deepcopy(item.node)
            node_type = node['node_type']
            if node_type in ('chapter', 'paragraph'):
                node.pop('children', '')
            if node_type == 'question':
                item.stop_iter_subs()
            node_list.append(node)
        return node_list

    def has_error(self, node):
        """
        判断 node 内是否有错误
        node 为大小题情况下，如果子题有 error，则大小题一起重新处理
        第一轮：所以 error 都跑
        第二轮：只跑白名单中的 error
        """
        error_list = []

        def recursion(d):
            nonlocal error_list
            error_info = d.get('errorInfo', [])
            error_list += error_info
            if d.get('children'):
                for c in d['children']:
                    recursion(c)

        recursion(node)
        if error_list:
            return True
        # for item in error_list:
        #     rule = item['rule']
        #     if self.round_index == 1:
        #         return True
        #     elif self.round_index > 1 and rule in ERROR_INFO_WHITE_LIST:
        #         return True
        return False

    def get_block(self, node, node_list, index):
        """
        根据 node 获取对应的 html 内容
        获取 node 中的最小的 line 和最大的 line，然后去 html_data 中切割
        """
        if node['node_type'] != 'question':
            return []
        serial_number = node.get('content', {}).get('serial_number', '')
        if serial_number in self.pred_error_block_question:
            return []

        # 如果当前题下面的段落 node 合并上来，确认下面 N 个 node
        node_length = len(node_list)
        next_node_str = ''
        for i in range(index + 1, index + 16):
            if i > node_length - 1:
                break
            next_node = node_list[i]
            next_node_type = next_node['node_type']
            if next_node_type in ('question', 'chapter'):
                # 拿到下一个
                next_node_str = str(next_node)
                break
            if next_node_type != 'paragraph':
                break
            next_body = next_node.get('content', {}).get('body', '')
            if not self.is_valid_node(next_body):
                break
            analysis = node['content']['analysis']
            node['content']['analysis'] = analysis + next_body

        data = str(node)
        min_line, max_line = self.get_line_range(data, next_node_str)
        if min_line is None and max_line is None:
            return []
        sub_html_list = self.html_list[min_line: max_line + 1]

        question_type = node.get('question_type', '')
        res = []
        # 材料题
        if question_type == QuestionType.material.name:
            sub_html_str = html_util.join_html(sub_html_list, is_del_line=False)
            prefix_text = html_util.get_prefix_text(sub_html_str)
            # debug
            # if '宿迁市宿豫区位于' in sub_html_str:
            res.append({
                'html_data': sub_html_str,
                'min_line': min_line,
                'max_line': max_line,
                'question_type': question_type,
                'serial_number': serial_number,
                'prefix_text': prefix_text,
            })
        # 其余题
        else:
            # 可能存在多题，拆分为单题一组
            sub_html_group = []
            tmp = []
            for sub_i, html_str in enumerate(sub_html_list):
                if '首先利用指对互化' in html_str:
                    print()

                if sub_i == 0 and 'data-level="1"' in html_str:
                    tmp.append(html_str)
                    if len(sub_html_list) == 1:
                        sub_html_group.append('\n'.join(tmp))
                elif 'data-level="1"' in html_str and 'data-label="explanation"' not in html_str and 'data-label="answer"' not in html_str:
                    sub_html_group.append('\n'.join(tmp))
                    tmp = [html_str]
                elif sub_i == len(sub_html_list) - 1:
                    tmp.append(html_str)
                    sub_html_group.append('\n'.join(tmp))
                else:
                    tmp.append(html_str)

            prefix_text_list = []
            for sub_html_str in sub_html_group:
                # debug
                # if '下表是我国2024年上半年' not in sub_html_str:
                #     continue

                # 重新判断题型
                question_type = self.get_question_type(sub_html_str, node.get('question_type', ''))
                min_line, max_line = self.get_line_range(sub_html_str)
                prefix_text = html_util.get_prefix_text(sub_html_str)
                serial_number = html_util.get_serial_number(sub_html_str)
                res.append({
                    'html_data': sub_html_str,
                    'min_line': min_line,
                    'max_line': max_line,
                    'question_type': question_type,
                    'serial_number': serial_number,
                    'prefix_text': prefix_text,
                })
                prefix_text_list.append(prefix_text)
            self.pred_error_block_question = prefix_text_list
        return res

    def get_question_type(self, data, question_type):
        """ 判断题型 """
        if question_type == QuestionType.other.name:
            if self._is_other(data):
                return QuestionType.other.name
            elif self._is_choice(data):
                return QuestionType.choice.name

        if question_type != QuestionType.blank.name:
            return question_type
        if 'data-label="quest_num"' in data and 'data-level="2"' in data:
            if self._is_material(data):
                return QuestionType.material.name
            else:
                return QuestionType.other.name
        else:
            if self._is_other(data):
                return QuestionType.other.name
        return question_type

    def is_valid_node(self, s):
        invalid_keywords = [r'答题卡', r'题号', r'本题包括']
        for k in invalid_keywords:
            if re.search(k, s):
                return False
        return True

    def get_line_range(self, data, next_node_str=''):
        """
        获取行号的范围
        # 获取最小行号和最大行号
        # 最小行号：data_line_list 的最小值
        # 最大行号：next_data_line_list 的最小值减一
        """
        data_line_list = re.findall(r' line="(\d+)"', data)
        next_data_line_list = re.findall(r' line="(\d+)"', next_node_str)
        if not data_line_list:
            return None, None

        # TODO 这里需要限制住，一个 sub_html_data 里面不能有两种题型
        # 获取最小行号和最大行号
        # 最小行号：data_line_list 的最小值
        # 最大行号：next_data_line_list 的最小值减一
        data_line_list = [int(l) for l in data_line_list]
        min_line = min(data_line_list)
        next_data_line_list = [int(l) for l in next_data_line_list]
        if not next_data_line_list:
            max_line = max(data_line_list)
        else:
            max_line = min(next_data_line_list) - 1
        # 试题结尾重新圈选
        while True:
            if not self.html_list[max_line].startswith('<p'):
                max_line -= 1
            else:
                break
        return min_line, max_line

    def _is_material(self, data):
        if '材料' in data:
            return True
        return False

    def _is_other(self, data):
        if re.search(r'<span[^<>]*?data-level="1"[^<>]*?>', data) and '（1）' in data and '（2）' in data:
            return True
        return False

    def _is_choice(self, data):
        data_lst = data.split('</p>')
        body = ''
        for d in data_lst:
            if 'data-label="answer"' in d:
                break
            body += d

        if 'A.' in body and 'B.' in body and 'C.' in body and 'D.' in body:
            return QuestionType.choice.name
