# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/12 15:43
import asyncio
import re
import ast
from bs4 import BeautifulSoup

from app.basic.util import html_util, utils
from app.enums.prompt import GetPrompt
from app.enums.subject import Subject
from app.enums.node import ReplText, REPL_TEXT_LIST
from app.basic.api.doubao import <PERSON>uba<PERSON>, DBModel
from app.basic.log import logger
from app.basic.baseerror import VerifyError, TokenTooMuchError


class FixErrorBlock:

    def __init__(self, html_list: list, error_block_list: list, task_id: str, subject: str):
        self.html_list = html_list
        self.error_block_list = error_block_list
        self.task_id = task_id
        self.subject = subject
        self.stat = {}

    async def main(self):
        """
        处理 check 失败的 block
        """
        # 并发执行，控制并发数
        error_block_chunk_list = utils.chunk_v2(self.error_block_list, 50)
        for item_chunk_list in error_block_chunk_list:
            tasks = [self.fix_block(item) for item in item_chunk_list]
            await asyncio.gather(*tasks)

    async def fix_block(self, block_item):
        block = block_item['html_data']
        min_line, max_line = block_item['min_line'], block_item['max_line']
        question_type = block_item['question_type']

        pred_block, json_data, model_res, sub_html_data = '', [], '', ''
        try:
            pred_block, block_tag_cache = self.pre_clean_block(block)
            model_res = await self.use_model_structure(pred_block, question_type)
            new_json_data = self.post_block(model_res, block_tag_cache)
            sub_html_data = self.json_to_html(new_json_data)
            # 检查大模型是否丢内容
            if not self.char_check(block, sub_html_data):
                raise VerifyError(f"大模型返回结构丢失内容")
            # 把所有的数据写到开始的 line，其余 line 置空
            for i in range(min_line, max_line + 1):
                if i == min_line:
                    self.html_list[i] = sub_html_data
                else:
                    self.html_list[i] = ''
            self.stat['fixed_block_count'] += 1
        except Exception as e:
            if 'TokenTooMuchError' in str(e):
                self.stat['fixed_block_count'] += 1
            logger.info(
                f">>>>>> task_id={self.task_id}\n"
                f">>>>>> pred_block={pred_block}\n"
                f">>>>>> model_res={model_res}\n"
                f">>>>>> error_info={utils.format_error()}\n"
                f"************************************************************************\n\n\n"
            )

    def pre_clean_block(self, block):
        """
        预清洗有问题的块
        """
        # block = """"""
        block_tag_cache = {}

        # table 表格放在最前面，因为表格内可能含有 blank bracket 等
        soup = BeautifulSoup(block, 'html.parser')
        tables = soup.find_all('table')
        for i, table in enumerate(tables, start=1):
            table_str = str(table)
            repl_text = f"#table{i}#"
            block_tag_cache[repl_text] = table_str
            table.replace_with(repl_text)
        block = str(soup)

        # 补齐大题题干
        block = re.sub(
            r'(<span[^<>]*?data-label="quest_num"[^<>]*?data-level="1"[^<>]*?>.*?</span>)(<span[^<>]*?data-label="quest_num"[^<>]*?data-level="2"[^<>]*?>.*?</span>)',
            lambda x: x.group(1) + f"{ReplText.BIG_QUESTION_BODY.value}\n" + x.group(2),
            block)
        # 如果题号前有内容，则加换行符
        block = re.sub(
            r'(<p[^<>]*?>)(.+?)(<span[^<>]*?data-label="quest_num"[^<>]*?data-level="\d"[^<>]*?>)',
            lambda x: x.group(1) + x.group(2) + '\n' + x.group(3),
            block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#bracket{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # bracket
        block = re.sub(r'<span[^<>]*?data-label="bracket"[^<>]*?>(?<!</span>)[\S\s]*?</span>', repl, block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#blank{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # blank
        block = re.sub(r'<span[^<>]*?data-label="blank"[^<>]*?>(?<!</span>)[\S\s]*?</span>', repl, block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#tag{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # img
        block = re.sub(r'<img[^<>]*?>', repl, block)

        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#latex{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        # 公式
        block = re.sub(r'\$\$.*?\$\$', repl, block)

        # 清理 p 中原本的语义化标签
        def repl(m):
            text = m.group()
            text = re.sub(r' block-tag="explanation\d+"', '', text)
            text = re.sub(r' data-label="explanation"', '', text)
            text = re.sub(r' block-tag="answer\d+"', '', text)
            text = re.sub(r' data-label="answer"', '', text)
            return text
        block = re.sub(r'<p[^<>]*?>', repl, block)

        # p 替换为 tag
        i = 1
        def repl(m):
            nonlocal i
            text = m.group()
            repl_text = f'#p{i}#'
            block_tag_cache[repl_text] = text
            i += 1
            return repl_text
        block = re.sub(r'<p[^<>]*?>', repl, block)

        # 清掉其余的无用标签
        block = re.sub(r'<[^<>]*?>', '', block)

        # 超过 token 限制，则直接报错
        if self.subject == Subject.english.name:
            length = len(re.split(r'\s+', block))
        else:
            length = len(block)
        if length > 5000:
            raise TokenTooMuchError('token 超过边界，不执行调用大模型')
        return block, block_tag_cache

    async def use_model_structure(self, block, question_type):
        """ 使用大模型结构化 """
        prompt = GetPrompt.text_to_json(block, self.subject, question_type)
        model_res, cost_token = await Doubao.async_chat(prompt, DBModel.DS_V3.value, 0.1)
        model_res = model_res.replace('```JSON', '').replace('```', '').replace('```json', '').replace('json', '')
        model_res = model_res.strip()
        self.stat['cost_token'] += cost_token
        return model_res

    def post_block(self, model_res, block_tag_cache):
        # 替换掉添加的文本
        for t in REPL_TEXT_LIST:
            model_res = model_res.replace(t, '')

        # 转成 python list
        try:
            json_data = ast.literal_eval(model_res)
        except Exception as e:
            print(f"model_res={model_res}")
            raise VerifyError(f"大模型返回结果结构化失败")

        def recursion(data):
            if isinstance(data, dict):
                return {key: recursion(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [recursion(item) for item in data]
            else:
                return repl(data)

        def repl(value):
            # 清掉 table 两边 p
            if '#table' in value:
                value = re.sub(r'<p>(#table\d+#)</p>', lambda x: x.group(1), value)
            # 把 tag 替换回去
            for tag, text in block_tag_cache.items():
                value = value.replace(tag, text)
            # 去掉空 p
            value = re.sub(r'<p[^<>]*?>\s*</p>', '', value)
            # 还原原 html 的 p
            value = re.sub(r'<p>(<p[^<>]*?>)', lambda x: x.group(1), value)
            # 补充 </p>
            if re.search(r'<p[^<>]*?>', value) and '</p>' not in value:
                value = value + '</p>'
            return value

        # 替换 tag
        json_data = recursion(json_data)
        return json_data

    def json_to_html(self, json_data):
        res = ''
        for d in json_data:
            d = self.pre_json_data(d)
            sub_html = self.sub_json_to_html(d)
            res += sub_html
        return res

    def sub_json_to_html(self, json_data):
        body_list = []
        choice_list = []
        answer_list = []
        analysis_list = []
        extra_list = []

        def recursion(data, depth=1):
            has_children = bool(data.get('children'))
            # 题号
            origin_serial_number = serial_number = data["serial_number"]
            if re.search(r'^\d+$', serial_number):
                serial_number = serial_number + '.'
            serial_number = f'<span data-label="quest_num" data-level="{depth}">{serial_number}</span>'
            body = data['body']
            if not body:
                body = f'<p>{serial_number}</p>'
            else:
                body = re.sub(r'<p[^<>]*?>', lambda x: x.group() + serial_number, body, count=1)
            body_list.append(body)

            # 选项
            choice = data.get('choice', [])
            if choice:
                for c in choice:
                    letter, option = c['letter'], c['option']
                    letter = f'<span data-label="choice_option">{letter}.</span>'
                    option = re.sub(r'<p[^<>]*?>', lambda x: x.group() + letter, option)
                    choice_list.append(option)

            # 答案
            # 小题答案需要拼接题号
            answer = data['answer'] if depth == 1 else data['child_answer']
            answer = [a for a in answer if a]
            answer_prefix = data.get('answer_prefix', '')
            if answer_prefix:
                answer_prefix = html_util.add_discard_span(answer_prefix)
            answer = '■'.join(answer)
            if depth > 1:
                answer = re.sub(rf'(<p[^<>]*?>)', lambda x: x.group(1) + serial_number, answer, count=1)
                answer = re.sub(r'</p>■<p[^<>]*?>', '■', answer)
            if answer:
                answer_prefix = f'<p data-label="answer">{answer_prefix}</p>' if answer_prefix else ''
                answer = answer.replace('<p', '<p data-label="answer"')
                answer_list.append(answer_prefix + answer)

            # 解析
            analysis = data['analysis'] if depth == 1 else data.get('child_analysis', '')
            # 解析预处理，去掉多余的题号
            analysis = re.sub(rf'(<p[^<>]*?>){origin_serial_number}', lambda x: x.group(1), analysis)
            analysis_prefix = data.get('analysis_prefix', '')
            if analysis_prefix:
                analysis_prefix = html_util.add_discard_span(analysis_prefix)
            # 大小题解析都需要拼接题号和前缀
            analysis = re.sub(r'<p[^<>]*?>', lambda x: x.group() + analysis_prefix + serial_number, analysis, count=1)
            # 确认是否有点睛，如果有放到 extra
            pattern = re.compile(r'<p[^<>]*?>\s*【点睛】')
            if depth == 2 and pattern.search(analysis):
                tmp = analysis
                extra_start_index = pattern.search(analysis).regs[0][0]
                analysis = tmp[:extra_start_index]
                extra = tmp[extra_start_index:]
                extra = extra.replace('<p', '<p data-label="extra"')
                extra_list.append(extra)
            analysis = analysis.replace('<p', '<p data-label="explanation"')
            if analysis:
                analysis_list.append(analysis)

            if has_children:
                for child in data['children']:
                    recursion(child, depth + 1)

        recursion(json_data)

        html_list = body_list + choice_list + answer_list + analysis_list + extra_list
        html_data = '\n'.join(html_list)
        html_data = html_data.replace('<p', '<p data-check="ai_check"')
        return html_data

    def char_check(self, block, model_res):
        """ 处理前后字符级别核对 HTML """
        # 预处理 model_res 有新增内容
        block = html_util.del_html_tag(block)
        model_res = html_util.del_html_tag(model_res)

        # 清除模型中可能会丢失的无用关键词，增加成功率
        keywords = ['答案', '答：', '【详解】解：', '【详解】', '解：']
        for k in keywords:
            block = block.replace(k, '')
            model_res = model_res.replace(k, '')

        # 对比中文和英文字符串数量
        block_cn_count, block_en_count = utils.count_chinese_english_chars(block)
        model_cn_count, model_en_count = utils.count_chinese_english_chars(model_res)
        if block_cn_count != model_cn_count:
            return False
        if block_en_count != model_en_count:
            return False

        # 对比各标签数量 #bracket  #blank 等
        if block.count('#bracket') != model_res.count('#bracket'):
            return False
        if block.count('#blank') != model_res.count('#blank'):
            return False
        if block.count('#image') != model_res.count('#image'):
            return False
        if block.count('#table') != model_res.count('#table'):
            return False
        if block.count('#latex') != model_res.count('#latex'):
            return False
        return True

    def pre_json_data(self, data):
        if not data.get('children'):
            return data

        # 增加了 child_analysis 变为数组，同时存储同题的大题解析和小题解析，把大题解析和小题解析还原到原字段
        parent_analysis_list = []
        # 标志大题解析中是否含有小题的题号
        first_parent_analysis_has_serial_number = True
        for index, item in enumerate(data['children']):
            body = item.get('body', '')
            analysis_prefix = item.get('analysis_prefix', [])
            child_analysis = item.get('child_analysis', [])
            serial_number = item.get('serial_number', '')

            if len(child_analysis) == 2:
                parent_analysis, analysis = child_analysis[0], child_analysis[1]
                # 判断如果 parent_analysis 没有题号，则合并到 analysis
                if serial_number not in parent_analysis and not first_parent_analysis_has_serial_number:
                    analysis = parent_analysis + analysis
                    parent_analysis = ''
            elif len(child_analysis) == 0:
                parent_analysis, analysis = '', ''
            else:
                parent_analysis, analysis = '', child_analysis[0]

            if index == 0 and serial_number not in parent_analysis:
                first_parent_analysis_has_serial_number = False

            if len(analysis_prefix) == 2:
                parent_prefix, child_prefix = analysis_prefix[0], analysis_prefix[1]
            elif len(analysis_prefix) == 0:
                parent_prefix, child_prefix = '', ''
            else:
                parent_prefix, child_prefix = '', analysis_prefix[0]

            def repl(m):
                text = m.group()
                # 现在大模型返回结果中冗余了题号，所以大题解析中不需要注定拼接题号
                return text + parent_prefix
            parent_analysis = re.sub(r'<p[^<>]*?>', repl, parent_analysis, count=1)
            parent_analysis_list.append(parent_analysis)
            # 小题解析中去掉多余的题号文本
            analysis = re.sub(rf'(<p[^<>]*?>){serial_number}', lambda x: x.group(1), analysis, count=1)
            data['children'][index]['child_analysis'] = analysis
            data['children'][index]['analysis_prefix'] = child_prefix
            # 小题题干中去掉多余的题号文本
            body = re.sub(rf'(<p[^<>]*?>){serial_number}', lambda x: x.group(1), body, count=1)
            data['children'][index]['body'] = body
        data['analysis'] = ''.join(parent_analysis_list)
        return data
