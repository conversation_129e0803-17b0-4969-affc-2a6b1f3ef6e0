# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/5 15:45
import re

from app.basic.util import html_util
from app.basic.log import logger


class CleanByLine:

    def __init__(self, html_data: str, subject: str, task_id: str):
        self.html_data = html_data
        self.html_list = html_util.split_html_v2(self.html_data)
        self.subject = subject
        self.task_id = task_id

    def main(self):
        logger.info(f"{self.task_id} CleanByLine start.")
        for index, s in enumerate(self.html_list):
            s = self.del_extra_text(s)

            self.html_list[index] = s

        self.html_data = html_util.join_html(self.html_list)
        logger.info(f"{self.task_id} CleanByLine success.")
        return self.html_data

    def del_extra_text(self, s: str):
        pred_s = html_util.del_html_tag(s)
        pred_s = re.sub(r'\s+', ' ', pred_s)
        pred_s = html_util.del_html_entities(pred_s)
        if '学校' in pred_s and '姓名' in pred_s and '班级' in pred_s and '考号' in pred_s and len(pred_s) < 20:
            s = re.sub(
                r'(<p[^<>]*?>)((?<!</p>)[\S\s]*?)(</p>)', lambda x:
                x.group(1) + f'<span data-label="discard">{html_util.add_check_span(x.group(2))}</span>' + x.group(3),
                s)
            return s
        else:
            return s
