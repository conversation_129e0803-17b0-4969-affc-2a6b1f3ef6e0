# -*-coding:utf-8 -*-
# Author     ：wang<PERSON>ua
# Email      ：<EMAIL>
# Time       ：2025/3/7 15:40
import re

from app.enums.app_key import AppKey
from app.basic.util import html_util
from app.basic.log import logger


class SemicByRules:

    def __init__(self, html_data: str, app_key: str, task_id: str):
        self.html_data = html_data
        self.app_key = app_key
        self.task_id = task_id
        self.html_list = html_util.split_html_v2(self.html_data)

    def main(self):
        logger.info(f"{self.task_id} SemicByRules start.")
        # 仅针对九学王-API 处理
        if self.app_key not in (AppKey.JXW_API.value, AppKey.HEXIN.value):
            return self.html_data

        # 拿到每一个题目，判断是小题还是大小题，根据大小题和小题分别进行处理
        queue_num_start_index = -1
        is_find_start = False
        queue_num_end_index = -1
        is_small_quest = True
        is_through_answer = False
        html_len = len(self.html_list)
        for index, s in enumerate(self.html_list):
            if 'data-label="quest_num"' in s and 'data-level="1"' in s and not is_find_start:
                queue_num_start_index = index
                is_find_start = True

            if index + 1 >= html_len:
                queue_num_end_index = index
            else:
                next_s = self.html_list[index + 1]
                if 'data-label="quest_num"' in next_s and 'data-level="1"' in next_s and is_find_start:
                    queue_num_end_index = index

            if 'data-label="quest_num"' in s and 'data-level="2"' in s and not is_through_answer:
                is_small_quest = False

            if 'data-label="answer"' in s:
                self.html_list[index] = self.del_answer_text(s)
                is_through_answer = True

            if queue_num_start_index > 0 and queue_num_end_index > 0:
                # 按照规则清洗这个题目
                if not is_small_quest:
                    index_count = queue_num_end_index - queue_num_start_index + 1
                    is_in_fenxi = False
                    is_in_xiangjie = False
                    parent_quest_num = ''
                    for i in range(index_count):
                        tmp = self.html_list[queue_num_start_index + i]
                        pred_tmp = re.sub(r'<[^<>]*?>', '', tmp)

                        # debug
                        if '在探究浮力规律时，实验小组设计了如图所示的实' in tmp:
                            print()

                        # 拿到大题题号
                        re_search = re.search(r'<span[^<>]*?data-label="quest_num"[^<>]*?data-level="1"[^<>]*?>(.*?)</span>', tmp)
                        if re_search:
                            parent_quest_num = re_search.group(1)

                        has_xiangjie, keywords = self.has_xiangjie(tmp)
                        # 分析前加大题题号
                        if self.has_fenxi(pred_tmp):
                            if 'data-check="ai_check"' not in tmp:
                                tmp = re.sub(
                                    r'<p[^<>]*?>', lambda x:
                                    x.group() + html_util.add_check_span(html_util.add_quest_num_span(parent_quest_num, '1')),
                                    tmp)
                            is_in_fenxi = True
                        elif has_xiangjie:
                            # 找到详解，删除
                            # 特殊情况：
                            # 1.可能有详解没有分析
                            if 'data-check="ai_check"' not in tmp:
                                tmp = tmp.replace(keywords, html_util.add_check_span(html_util.add_discard_span(keywords)))
                            is_in_fenxi = False
                            is_in_xiangjie = True

                        # 删除分析下的小题题号标签
                        # 除一级标题外都删除
                        if is_in_fenxi:
                            if 'data-check="ai_check"' not in tmp:
                                tmp = re.sub(
                                    r'<span[^<>]*?data-label="quest_num"[^<>]*?data-level="[^1]*?"[^<>]*?>(.*?)</span>',
                                    lambda x: html_util.add_check_span(x.group(1)),
                                    tmp)

                        # 详解下，如果没有小题标签，则加上
                        if is_in_xiangjie:
                            pattern = re.compile(r'（\d）')
                            if 'data-label="quest_num"' not in tmp and pattern.search(tmp):
                                if 'data-check="ai_check"' not in tmp:
                                    tmp = re.sub(
                                        r'（\d）',
                                        lambda x: html_util.add_check_span(html_util.add_quest_num_span(x.group(), '2')),
                                        tmp, count=1)

                        # 点睛改为附加内容
                        if self.has_dianjing(pred_tmp):
                            tmp = tmp.replace('data-label="explanation"', 'data-label="extra"')
                            if 'data-check="ai_check"' not in tmp:
                                tmp = html_util.add_check_span(tmp)

                        self.html_list[queue_num_start_index + i] = tmp

                # 刷新各个索引
                queue_num_start_index = -1
                is_find_start = False
                queue_num_end_index = -1
                is_small_quest = True
                is_through_answer = False

        self.html_data = html_util.join_html(self.html_list, is_del_line=False)
        logger.info(f"{self.task_id} SemicByRules success.")
        return self.html_data

    def del_answer_text(self, s):
        # 注意：keyword_list 的值有固定顺序，不可以随意修改
        if 'data-check="ai_check"' in s:
            return s

        keyword_list = ['【答案】：', '【答案】', '答案■', '答案']
        for k in keyword_list:
            if k in s:
                repl_str = html_util.add_check_span(html_util.add_discard_span(k))
                s = s.replace(k, repl_str)
                break
        return s

    def has_dianjing(self, pred_s):
        res = re.search(r'^\s*(点睛)|(【点睛】)', pred_s)
        return res

    def has_fenxi(self, pred_s) -> bool:
        pattern_list = [
            r'分析', f'【详解】解：'
        ]
        s = pred_s[:10]
        for p in pattern_list:
            res = re.search(p, s)
            if res:
                return True
        return False

    def has_xiangjie(self, s):
        keyword_list = ['详解：', '【详解】', '答：', '答:']
        for k in keyword_list:
            if k in s:
                return True, k
        return False, ''


if __name__ == '__main__':
    import requests
    h = (requests.get('https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/task/19018509.machine.html?time=1742270225054')
         .content.decode('utf-8'))
    r = SemicByRules(
        html_data=h
    ).main()
