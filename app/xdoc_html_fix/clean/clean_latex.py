import re

from app.enums.subject import SubjectUtil, Subject
from app.basic.util import latex_util, html_util
from app.enums.tag import LatexTag
from app.basic.log import logger


class CleanLatex:

    """
    LATEX 预处理类
    原则：宁愿错过，不要误伤
    """

    def __init__(self, html_data, subject):
        self.html_data = html_data
        self.subject = subject

    def main(self):
        logger.info(f"CleanLatex start.")
        self.complete_latex()
        self.text_to_latex()
        self.up_down_tag_fix()

        # Tips 加 latex 修改 tag 思路
        # 在做 latex 预处理过程中，把动过的 latex 加一个特殊字符
        # 把 HTML 中 latex 中的特殊字符替换为 html 标签，注意，特殊字符在一个 latex 可能有多个，替换完后需要执行一次全部删除
        def repl(m):
            text = m.group()
            if LatexTag.CHANGED.value in text:
                text = html_util.add_check_span(text)

            # 把应该挪出来的内容挪出来
            text = re.sub(r'(\$\$.*?)&&(.*?)&&(.*?\$\$)', lambda x: x.group(2) + x.group(1) + x.group(3), text)
            return text
        self.html_data = re.sub(r'\$\$.*?\$\$', repl, self.html_data)
        self.html_data = self.html_data.replace(LatexTag.CHANGED.value, '')

        logger.info(f"CleanLatex success.")
        return self.html_data

    def complete_latex(self):
        """
        补全 latex
        符号数字之类的没有进公式
        """
        """ 第一次合并，先把必然正确的合并到 $$ 内 """
        # 提取所有的 latex
        self.html_data, latex_list = latex_util.extract_latex(self.html_data)

        # - 被 span 包裹，去掉 span
        self.html_data = re.sub(r'<span[^<>]*?>-</span>', '-', self.html_data)
        # case: （120-$$###$$、$$###$$26
        # $$ 前后的满足条件的，合并到 $$ 前后
        def repl(m):
            pre_text = m.group(1)
            post_text = m.group(2)
            result = f"$${pre_text}###{post_text}$$"
            if pre_text or post_text:
                result = re.sub(r'^\$\$', lambda x: x.group() + LatexTag.CHANGED.value, result)
            return result
        self.html_data = re.sub(r'([（a-z\d.\-＜=]*)\$\$###\$\$([a-z\d.\-）\]＞=]*)', repl, self.html_data)
        self.html_data = re.sub(r'(\d+：)\$\$###\$\$(：\d+)', repl, self.html_data)

        # 还原 latex
        self.html_data = latex_util.restore_latex_v1(self.html_data, latex_list)

        self._del_adjoin_dollar()

        """ 第二次合并，把不保险的按照 case 合并 """
        # case: $$t=6\sqrt{2}∈[2\sqrt{10}$$，$$4\sqrt{5}]$$
        self.html_data = re.sub(
            r'(\[[^]]*?)\$\$(，)\$\$([^[]*?\])',
            lambda x: LatexTag.CHANGED.value + x.group(1) + x.group(2) + x.group(3),
            self.html_data)
        # case: $$\mathrm{Fe}(\mathrm{OH})_{3}($$胶体$$)+3\mathrm{HCl},\mathrm{C}$$
        # 本 case 严格的依赖第一个 latex 的最后一个括号和第二个 latex 的第一个括号
        self.html_data = re.sub(
            r'(\()\$\$([\u4e00-\u9fff]*?)\$\$(\))',
            lambda x: LatexTag.CHANGED.value + x.group(1) + x.group(2) + x.group(3),
            self.html_data)

    def text_to_latex(self):
        """
        理科内存在连续字母两边没有加 $$ 的，加上 $$
        这部分不加 check 标签，无法确认具体修改了哪个
        """
        if not SubjectUtil.is_li(self.subject):
            return

        # 提取出 latex 和 html 标签
        self.html_data, latex_list = latex_util.extract_latex(self.html_data)

        tag_list = []
        def repl(m):
            tag_list.append(m.group(1))
            return '<###>'
        self.html_data = re.sub(r'<([^<>]*?)>', repl, self.html_data)

        # 把所有的满足条件的替换，各学科正斜体规则不一致，注意区分
        def repl(m):
            text = m.group()
            result = latex_util.convert_to_latex(text, self.subject)
            return result
        self.html_data = re.sub(r'[A-Z]+', repl, self.html_data)

        # 还原 HTML 标签
        i = 0
        def repl(m):
            nonlocal i
            text = m.group()
            text = text.replace('###', tag_list[i])
            i += 1
            return text
        self.html_data = re.sub(r'<###>', repl, self.html_data)

        # 把不满足条件的还原
        # 还原选项 <span data-label="choice_option">D.</span>
        def repl(m):
            text = m.group(2)
            text = latex_util.convert_to_text(text, self.subject, '[A-F]')
            result = m.group(1) + text + m.group(3)
            return result
        self.html_data = re.sub(
            r'(<span[^<>]*?data-label="choice_option"[^<>]*?>)((?<!</span>)[\S\s]*?)(</span>)',
            repl, self.html_data)
        # 答案中的指定字母恢复
        def repl(m):
            text = m.group(2)
            text = latex_util.convert_to_text(text, self.subject, '[A-F]{,3}')
            result = m.group(1) + text + m.group(3)
            return result
        self.html_data = re.sub(r'(<p[^<>]*?data-label="answer"[^<>]*?>)((?<!</p>)[\S\s]*?)(</p>)', repl, self.html_data)
        # 详解中的指定字母恢复
        def repl(m):
            text = m.group(2)
            text = latex_util.convert_to_text(text, self.subject, '[A-F]')
            result = m.group(1) + text + m.group(3)
            return result
        self.html_data = re.sub(r'(<p[^<>]*?data-label="explanation"[^<>]*?>)((?<!</p>)[\S\s]*?)(</p>)', repl, self.html_data)
        # 根据规则还原选项字母
        pattern_list = [
            r'\$\$%s[A-F]+\$\$不?正确', r'\$\$%s[A-F]+\$\$\.', r'故选?[：:]?\$\$%s[A-F]+\$\$', r'\$\$%s[A-F]+\$\$不?符合'
        ]
        for p in pattern_list:
            def repl(m):
                text = m.group()
                # text = text.replace('$$', '')
                text = latex_util.convert_to_text(text, self.subject, '[A-F]+')
                return text
            p = p % f'{LatexTag.CHANGED.value}?'
            self.html_data = re.sub(p, repl, self.html_data)

        # TODO 这里可能还有其他的情况需要还原，暂时没想到

        # 还原 latex
        self.html_data = self.html_data.replace(f'{LatexTag.CHANGED.value}$$', f'$${LatexTag.CHANGED.value}')
        self.html_data = latex_util.restore_latex_v2(self.html_data, latex_list)

    def up_down_tag_fix(self):
        # 上下标问题处理
        self.html_data, latex_list = latex_util.extract_latex(self.html_data)

        # case: $$x_{2}′=\frac{2\times 2}{2} \mathrm{\;m}=2 \mathrm{\;m}$$
        # 期望：$$x_{2}^{\prime}=\frac{2\times 2}{2} \mathrm{\;m}=2 \mathrm{\;m}$$
        for index, l in enumerate(latex_list):
            pattern = re.compile(r'([^\^])′')
            if pattern.search(l):
                l = pattern.sub(lambda x: x.group(1) + '^{\\prime}', l)
                l = l.replace('′', '\\prime')
                l = LatexTag.CHANGED.value + l
                latex_list[index] = l

            # TODO 需要挪出来
            # 把不应该存在在 latex 的文本去掉
            latex_list[index] = re.sub(r'^\d：', lambda x: f"&&{x.group()}&&", l)

        self.html_data = latex_util.restore_latex_v2(self.html_data, latex_list)


    def _del_adjoin_dollar(self):
        # 清理完成后，会产生连续多个 latex 的情况
        # 例如输入：y=3\sqrt{2a}$$<span>-</span>6$$＋\frac{1}{4}$$（120<span>-</span>$$a)＋2=3\sqrt{2a}$$<span>-</span>$$\frac{1}{4}a＋$$26
        # 清理完成后：y=3\\sqrt{2a}$$$$-6＋\\frac{1}{4}$$$$（120-a)＋2=3\\sqrt{2a}$$$$-\\frac{1}{4}a＋$$26
        # 但这多个 latex 公式其实是一个
        self.html_data = self.html_data.replace('$$$$', '')
