import re
import asyncio
import time

from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.enums.subject import SubjectUtil, Subject
from app.basic.util import latex_util
from app.basic.api.xdoc import XdocA<PERSON>
from app.enums.prompt import GetPrompt
from app.basic.util import html_util, utils
from app.enums.tag import LatexTag
from app.basic.log import logger
from app.basic.baseerror import VerifyError


class LatexErrorFix:
    def __init__(self, html_data: str, subject: str, task_id: str):
        self.html_data = html_data
        self.subject = subject
        self.task_id = task_id
        self.subject_name = SubjectUtil.get_subject_name(self.subject)
        self.stat = {'cost_token': 0}

    async def main(self):
        try:
            start_time = time.time()
            logger.info(f"{self.task_id} LatexErrorFix start.")
            await self.fix_error()

            # 加 latex 修改 tag 思路同 latex_pre
            def repl(m):
                text = m.group()
                if LatexTag.CHANGED.value in text:
                    text = html_util.add_check_span(text)
                return text
            self.html_data = re.sub(r'\$\$.*?\$\$', repl, self.html_data)
            self.html_data = self.html_data.replace(LatexTag.CHANGED.value, '')
            end_time = time.time()
            self.stat.update({'cost_time': int(end_time - start_time)})
            logger.info(f"{self.task_id} LatexErrorFix success.")
            return self.html_data, self.stat
        except Exception as e:
            error_info = f'LatexErrorFix error {utils.format_error()}'
            raise VerifyError(f"{self.task_id} {error_info}")

    async def fix_error(self):
        # 提起 latex，监测是否可解析
        self.html_data, latex_list = latex_util.extract_latex(self.html_data)
        latex_check_list = XdocApi.formula_check(latex_list)

        # 拿出错误的 latex 并发执行
        error_latex_list = {}
        for index, item in enumerate(latex_check_list):
            item['index'] = index
            latex_str, is_correct = item['latex_str'], item['is_correct']
            if is_correct and self.is_correct_latex(latex_str):
                continue
            item['is_correct'] = False
            error_latex_list[index] = item

        logger.info(f"{self.task_id} LatexErrorFix error latex 总数 {len(error_latex_list)}")
        for batch, start_index in utils.chunk(error_latex_list.values(), 50):
            tasks = [self.call_chat(b) for b in batch]
            results = await asyncio.gather(*tasks)

            for result in results:
                is_success = result[0]
                index = result[1]['index']
                if is_success:
                    error_latex_list[index]['new_latex_str'] = result[1]['new_latex_str']

        new_latex_list = []
        for item in latex_check_list:
            index = item['index']
            if error_latex_list.get(index):
                new_latex_list.append(LatexTag.CHANGED.value + error_latex_list[index]['new_latex_str'])
            else:
                new_latex_list.append(item['latex_str'])

        assert len(new_latex_list) == len(latex_list)
        self.html_data = latex_util.restore_latex_v2(self.html_data, new_latex_list)

    async def call_chat(self, latex_item):
        latex_str = f"$${latex_item['latex_str']}$$"
        prompt = GetPrompt.fix_latex_error(latex_str)
        try:
            model_res, cost_token = await Doubao.async_chat(prompt, DBModel.V15_PRO_32.value, 0.1)
            self.stat['cost_token'] += cost_token
            new_latex = re.search(r'\$\$(.*?)\$\$', model_res).group(1)
            latex_item['new_latex_str'] = new_latex
            return True, latex_item
        except Exception as e:
            latex_item['new_latex_str'] = ''
            return False, latex_item

    def is_correct_latex(self, latex_str):
        if self.subject == Subject.chemistry.name:
            if '\\underset' in latex_str:
                return False
        return True
