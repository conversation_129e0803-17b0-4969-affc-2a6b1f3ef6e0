# -*-coding:utf-8 -*-
# Author     ：wang<PERSON>ua
# Email      ：<EMAIL>
# Time       ：2025/3/26 15:43
from enum import Enum


class QuestionType(Enum):
    choice = "选择题"
    blank = '填空题'
    other = '作答题'
    material = '材料题'
    true_or_false = '判断题'


class ReplText(Enum):
    BIG_QUESTION_BODY = '这是大题题干'


REPL_TEXT_LIST = [member.value for member in ReplText]


class QuestionTypeUtil:
    QUESTION_TYPE_UTIL = {member.name: member.value for member in QuestionType}

    @classmethod
    def get_name(cls, question_type):
        return cls.QUESTION_TYPE_UTIL[question_type]


ERROR_INFO_WHITE_LIST = [
    'multi_source',  # 试题来源不应该有多个
    'paragraph_content_error',  # 段落内容异常
    'question_content_empty',  # 题干内容为空
    'serial_number_empty',  # 题号为空
    'serial_number_format_invalid',  # 题号错误
    'serial_number_format_mismatch_neighbor',  # 题号格式错误
    'blank_width_choice',  # 填空题有选项
    'error_body_option',  # 题干中包含选项
    'answer_blank',  # 答案中包含横线
    'duplicate_answer',  # 答案重复
    'blank_answer_mismatch',  # 填空题答案与空数量不匹配
    'options_invalid',  # 选择题选项数量错误
]

