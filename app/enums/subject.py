# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/3 18:06
from enum import Enum


class Subject(Enum):
    chinese = '语文'
    math = '数学'
    english = '英语'
    physics = '物理'
    chemistry = '化学'
    biology = '生物'
    politics = '政治'
    history = '历史'
    geography = '地理'
    daode_fazhi = '道德与法治'


class SubjectUtil:
    SUBJECT_UTIL = {member.name: member.value for member in Subject}

    @classmethod
    def get_subject_name(cls, subject):
        return cls.SUBJECT_UTIL[subject]

    @classmethod
    def get_li_subject(cls):
        return [Subject.math.name, Subject.physics.name, Subject.chemistry.name, Subject.biology.name]

    @classmethod
    def is_li(cls, subject: str):
        return subject in cls.get_li_subject()
