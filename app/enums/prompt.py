from enum import Enum
from app.basic.storagehandler import storage
from app.enums.subject import Subject, SubjectUtil
from app.enums.node import QuestionType, QuestionTypeUtil
from app.basic.baseerror import ParamsError


class PromptType(Enum):
    TITLE_CHECK = 'static/ai-util/prompt/title_check.txt'
    XDOC_TITLE_LEVEL = 'static/ai-util/prompt/xdoc_title_level.txt'
    FIX_LATEX_ERROR = 'static/ai-util/prompt/fix_latex_error.txt'
    FIX_LATEX_ZX_MATH = 'static/ai-util/prompt/fix_latex_zx_math.txt'
    FIX_LATEX_ZX_PHYSICS = 'static/ai-util/prompt/fix_latex_zx_physics.txt'
    FIX_LATEX_ZX_CHEMISTRY = 'static/ai-util/prompt/fix_latex_zx_chemistry.txt'
    FIX_LATEX_ZX_BIOLOGY = 'static/ai-util/prompt/fix_latex_zx_biology.txt'

    # xdoc 使用大模型结构化试题
    CHOICE_TO_JSON = 'static/ai-util/prompt/choice_to_json.txt'
    BLANK_TO_JSON = 'static/ai-util/prompt/blank_to_json.txt'
    OTHER_TO_JSON = 'static/ai-util/prompt/other_to_json.txt'
    CHOICE_TO_JSON_V2 = 'static/ai-util/prompt/choice_to_json_v2.txt'
    BLANK_TO_JSON_V2 = 'static/ai-util/prompt/blank_to_json_v2.txt'
    OTHER_TO_JSON_V2 = 'static/ai-util/prompt/other_to_json_v2.txt'
    MATERIAL_TO_JSON_V2 = 'static/ai-util/prompt/material_to_json_v2.txt'
    ANALYSIS_FIX = 'static/ai-util/prompt/analysis_fix.txt'
    ANSWER_FIX = 'static/ai-util/prompt/answer_fix.txt'


class GetPrompt:

    @classmethod
    def title_check(cls, title_str):
        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=PromptType.TITLE_CHECK.value,
        )
        text = text.replace(':input', title_str)
        return text

    @classmethod
    def xdoc_title_level(cls, title_str):
        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=PromptType.XDOC_TITLE_LEVEL.value,
        )
        text = text.replace(':input', title_str)
        return text

    @classmethod
    def fix_latex_error(cls, latex_str):
        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=PromptType.FIX_LATEX_ERROR.value,
        )
        text = text.replace(':latex_str', latex_str)
        return text

    @classmethod
    def fix_latex_zx(cls, latex_input, count, subject):
        if subject == Subject.math.name:
            object_name = PromptType.FIX_LATEX_ZX_MATH.value
        elif subject == Subject.physics.name:
            object_name = PromptType.FIX_LATEX_ZX_PHYSICS.value
        elif subject == Subject.chemistry.name:
            object_name = PromptType.FIX_LATEX_ZX_CHEMISTRY.value
        elif subject == Subject.biology.name:
            object_name = PromptType.FIX_LATEX_ZX_BIOLOGY.value
        else:
            raise ParamsError(f"学科 {subject} 不支持！")

        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=object_name,
        )
        latex_input = latex_input.strip()
        text = text.replace(':count', str(count))
        text = text.replace(':input', latex_input)
        return text

    @classmethod
    def text_to_json(cls, block: str, subject: str, question_type: str, version: str = 'v1'):
        if version == 'v1':
            if question_type == QuestionType.choice.name:
                object_name = PromptType.CHOICE_TO_JSON.value
            elif question_type == QuestionType.blank.name:
                object_name = PromptType.BLANK_TO_JSON.value
            elif question_type == QuestionType.other.name:
                object_name = PromptType.OTHER_TO_JSON.value
            else:
                raise ParamsError(f'确少 {question_type} 的 prompt')
        else:
            if question_type == QuestionType.choice.name:
                object_name = PromptType.CHOICE_TO_JSON_V2.value
            elif question_type == QuestionType.blank.name:
                object_name = PromptType.BLANK_TO_JSON_V2.value
            elif question_type == QuestionType.other.name:
                object_name = PromptType.OTHER_TO_JSON_V2.value
            elif question_type == QuestionType.material.name:
                object_name = PromptType.MATERIAL_TO_JSON_V2.value
            else:
                raise ParamsError(f'确少 {question_type} 的 prompt')

        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=object_name,
        )
        text = text.replace(':input', block)
        subject_name = SubjectUtil.get_subject_name(subject)
        text = text.replace(':subject', subject_name)
        question_name = QuestionTypeUtil.get_name(question_type)
        text = text.replace(':question_type', question_name)
        return text

    @classmethod
    def analysis_fix(cls, input_data):
        """
        获取AI判断explanation的prompt模板

        Args:
            input_data: 需要判断的数据内容

        Returns:
            str: 完整的prompt文本
        """
        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=PromptType.ANALYSIS_FIX.value,
        )
        text = text.replace('### **Input**', f'### **Input**\n{input_data}')
        return text

    @classmethod
    def answer_fix(cls, input_data):
        """
        获取AI判断answer的prompt模板

        Args:
            input_data: 需要判断的数据内容

        Returns:
            str: 完整的prompt文本
        """
        text = storage.get_object_to_string(
            bucket_name='hexin-worksheet',
            object_name=PromptType.ANSWER_FIX.value,
        )
        text = text.replace('### **Input**', f'### **Input**\n{input_data}')
        return text

