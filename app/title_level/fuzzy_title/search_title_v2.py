# -*-coding:utf-8 -*-
# Author     ：wang<PERSON>ua
# Email      ：<EMAIL>
# Time       ：2025/5/12 21:24
# -*- coding: utf-8 -*-
import os
import re
import sys
import time
import json
import random
import requests
from pymilvus import MilvusClient, DataType


def create_embeddings(text):
    url = "https://api.siliconflow.cn/v1/embeddings"
    payload = {
        "model": "BAAI/bge-m3",
        "input": text,
        "encoding_format": "float"
    }
    list_tokens = [
        "sk-ivorocfzbodirzyikjlbkbwecmwaupgxdfkquepgvljqiqqn",  # 137
        "sk-xqjqqculezgsvllfaqyauimosuuzegotlmqpgsttglywbrpy",  # 世华
        "sk-zipeniqgicpsizrnixxklwrkjkhywifssegbrhheffzzxmnb",  # 152
        "sk-ojisqocguccemlifguwnnanotxwloffdhkhobgfanwzwurqo",  # 镕争
        "sk-bgmgjsfwzkiczpcnrrotmmxdydtridozsehajdrugfavcnnm",  # 明纯
        "sk-hdntfcmmbjyncomlxmdzcrkboqvwlvyveezkicchoomdtnda",  # 凯旋
        "sk-zszkkcvmxeoexxgdliefomfixptpplmkvxtulhifsykijqlz",  # 鹏洋
    ]
    headers = {
        "Authorization": "Bearer " + random.choice(list_tokens),
        "Content-Type": "application/json"
    }
    res = requests.request("POST", url, json=payload, headers=headers).json()['data'][0]['embedding']
    return res


def query_title(title_text):
    milvus_url = f"http://************:19530"
    client = MilvusClient(uri=milvus_url)
    query_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
    db_name = f"header_set"
    results = client.search(
        collection_name=db_name,
        data=[create_embeddings(title_text)], # 使用核心查询向量
        anns_field="text_vector",
        params=query_params,
        limit=15,
        output_fields=["text"]  # 返回结果集合
    )
    client.close()
    for _, hits in enumerate(results):
        for hit in hits:
            distance = hit['distance']
            if distance > 0.80:
                return True
    return False


if __name__ == '__main__':
    header_text = "'三、实验题:本题共1小题，共12分。将符合题意的内容填写在题目中的横线上,或按题目要求作答。'"
    res = query_title(header_text)
    print(res)
