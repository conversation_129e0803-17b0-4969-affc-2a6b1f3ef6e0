# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/2/28 17:23
import re
import time
import unicodedata
import os

import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from sklearn.preprocessing import normalize

from app.basic.log import logger


class TitleSearch:

    def __init__(self):
        start_time = time.time()
        self.model = SentenceTransformer('/mnt/d/model_local_cache/all-MiniLM-L6-v2')  # 轻量级模型
        end_time = time.time()
        logger.info(f"载入模型完毕, cost_time={end_time - start_time}")

        current_dir = os.path.dirname(os.path.abspath(__file__))
        title_index_path = os.path.join(current_dir, "title_index.faiss")
        title_set_path = os.path.join(current_dir, "normalized_title_set.txt")
        origin_title_path = os.path.join(current_dir, "title.txt")
        if os.path.exists(title_index_path) and os.path.exists(title_set_path):
            # 加载索引和向量
            self.index = faiss.read_index(title_index_path)
            with open(title_set_path, 'r', encoding='utf-8') as f:
                self.normalized_title_set = f.read().splitlines()
        else:
            with open(origin_title_path, 'r', encoding='utf-8') as f:
                origin_title_list = f.read().splitlines()

            # 创建两个数据结构
            self.normalized_title_set = set()
            preprocessed_titles = []

            for title in origin_title_list:
                self.normalized_title_set.add(title)
                preprocessed_titles.append(title)
            with open(title_set_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(list(self.normalized_title_set)))
            logger.info("数据加载完毕")

            # 向量化处理
            title_vectors = self.model.encode(preprocessed_titles)
            logger.info("向量化处理完毕")

            # 向量归一化（余弦相似度=内积）
            title_vectors = normalize(title_vectors, norm='l2', axis=1)
            logger.info("向量归一化完毕")

            # 构建、缓存索引和向量
            dimension = title_vectors.shape[1]
            self.index = faiss.IndexFlatIP(dimension)  # 内积索引
            self.index.add(title_vectors.astype('float32'))  # Faiss需要float32格式
            faiss.write_index(self.index, title_index_path)
            logger.info("构建 Faiss 索引完毕")

    def calculate_title_probability(self, query_str, top_k=3):
        # 标题模糊查询函数

        # 预处理查询字符串
        processed_query = self.preprocess(query_str)

        # 1. 完全匹配检测
        if processed_query in self.normalized_title_set:
            calibrated_score = 1.0
        else:
            # 2. 语义相似度检测
            # 生成查询向量
            query_vector = self.model.encode([processed_query])
            query_vector = normalize(query_vector, norm='l2', axis=1).astype('float32')

            # 相似度搜索（返回top_k个结果）
            distances, indices = self.index.search(query_vector, top_k)

            # 取最大相似度值
            max_similarity = np.max(distances)

            # 可选：对相似度进行后处理（示例简单线性缩放）
            calibrated_score = max(0.0, min(1.0, max_similarity))

        logger.info(f"{query_str} *** {processed_query} *** score={calibrated_score}")
        return calibrated_score

    @classmethod
    def preprocess(cls, text):
        # 预处理函数
        # 在处理 title.txt 数据的时候，title_str 需要用此函数来清洗
        # 在执行模糊查询的时，输出的 title_str 也需要用此函数来清洗

        # Unicode 标准化
        text = unicodedata.normalize('NFKC', text)  # 合并兼容字符
        # 保留有意义符号：@#&+，去除其他特殊符号
        text = re.sub(r"[^\w\s@#&+']", '', text)
        text = text.strip().lower()
        text = re.sub(r'\s+', ' ', text).strip()
        # 数字格式统一
        num_map = {'零': '0', '一': '1', '二': '2', '三': '3', '四': '4',
                        '五': '5', '六': '6', '七': '7', '八': '8', '九': '9'}
        pattern_num = re.compile(r'[零一二三四五六七八九]')
        pattern_num.sub(lambda x: num_map[x.group()], text)
        if re.search(r'[：（]', text):
            text = re.split(r'[：（]', text)[0]
            text = re.sub("[\dⅠⅡⅢⅣⅤⅥⅦ]", "X", text)
        return text


if __name__ == '__main__':
    title_search = TitleSearch()

    test_cases = [
        "02课堂小测",  # 完全匹配
        "精讲精练",  # 大小写不同
        "知识点2 轴对称图形",  # 符号变体
        "Animal Farm",  # 不在库中的标题
    ]

    for query in test_cases:
        is_title = title_search.calculate_title_probability(query)
