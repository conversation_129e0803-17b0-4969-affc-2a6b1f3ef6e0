# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/2/25 14:35
import ast
import time

from app.basic.api.doubao import <PERSON>ubao, DBModel
from app.basic.log import logger
from app.enums.prompt import GetPrompt
from app.basic.baseerror import VerifyError


class TitleSort:

    def __init__(self, task_id: str, title_list: list):
        self.task_id = task_id
        self.title_list = title_list

        self.stat = {
            'cost_token': 0,
            'cost_time': 0
        }

    def main(self):
        # 标题层级排序
        start_time = time.time()
        logger.info(f"{self.task_id} TitleSort stat")

        if not self.title_list:
            logger.info(f"{self.task_id} TitleSort 没有 title")
            return [], self.stat

        title_input = ''
        title_list_len = len(self.title_list)
        for index, item in enumerate(self.title_list):
            tmp = f"{item['title_str']}###{item['line_num']}"
            if index == title_list_len - 1:
                title_input += tmp
            else:
                title_input += f'{tmp}\n'

        structure_res, cost_token = self.call_model(title_input)
        logger.info(f"{self.task_id} TitleSort success")
        end_time = time.time()
        self.stat.update({
            'cost_token': cost_token,
            'cost_time': end_time - start_time,
        })
        return structure_res, self.stat

    def call_model(self, title_str: str):
        prompt = GetPrompt.xdoc_title_level(title_str)
        model_res, cost_token = Doubao.chat(prompt, DBModel.DS_V3.value, 0.1)
        model_res = model_res.replace('```JSON', '').replace('```', '').replace('```json', '').replace('json', '')

        # 转成 python list
        try:
            json_data = ast.literal_eval(model_res)
        except Exception as e:
            print(f"model_res={model_res}")
            raise VerifyError(f"大模型返回结果结构化失败")

        # 检查 json 结构是否正确
        try:
            for item in json_data:
                level, line_num = item['level'], item['line_num']
                item['level'] = int(level)
                item['line_num'] = int(line_num)
        except Exception as e:
            raise VerifyError(f"{self.task_id} model json data check error，json_data={json_data}")

        return json_data, cost_token
