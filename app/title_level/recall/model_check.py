# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/2/27 20:48
import ast
import time

from app.basic.api.doubao import <PERSON>ubao, DBModel
from app.basic.log import logger
from app.enums.prompt import GetPrompt
from app.basic.baseerror import VerifyError


class ModelCheck:

    def __init__(self, title_list, task_id):
        self.title_list = title_list
        self.task_id = task_id

    def main(self):
        start_time = time.time()
        title_list_len = len(self.title_list)
        logger.info(f"{self.task_id} ModelCheck before length: {title_list_len}")
        title_input = ''
        title_verified_list = []  # 确定为标题的部分，不需要进大模型做处理
        for index, item in enumerate(self.title_list):
            if item['is_title_verified']:
                title_verified_list.append(item)
                continue

            tmp = f"{item['title_str']}" + "###" + str(item['line_num'])
            if index == title_list_len - 1:
                title_input += tmp
            else:
                title_input += f'{tmp}\n'

        if not title_input:
            new_title_list = title_verified_list
            cost_token = 0
        else:
            new_title_list, cost_token = self.call_model(title_input)
            new_title_list += title_verified_list

        # 重新排序
        new_title_list = sorted(new_title_list, key=lambda x: x['line_num'])
        logger.info(f"{self.task_id} ModelCheck after length: {len(new_title_list)}")
        end_time = time.time()
        stat = {
            "cost_token": cost_token,
            "cost_time": int(end_time - start_time)
        }
        return new_title_list, stat

    def call_model(self, title_str: str):
        prompt = GetPrompt.title_check(title_str)
        model_res, cost_token = Doubao.chat(prompt, DBModel.DS_V3.value, 0.1)

        # prompt = f'''
        # # Task
        # 以下文本是我从某本书里摘出来的，每行末尾的###\\d+表示它在书里的行号，请帮我对以下文本进行筛选，区分出标题文本和非标题文本，把这本书的目录结构提取出来。

        # ```input
        # {title_str}
        # ```

        # # Rules
        # -每行后面的###\\d+表示该行在图书里的行号
        # -注意标题在结构上可能是存在连续性的

        # # Fewshots
        # ## Input
        # 广东省湛江市第二十一中学2024-2025学年高一下学期4月月考地理试题###1
        # 学校：姓名：班级：考号：###2
        # 一、单选题###3
        # 11.​读图可知，工作日武汉市中心城区高热区面积在13：00～14：00达到峰值...###8
        # 【点睛】“六看法”判断城市三大功能区（1）看面积：...###10
        # 二、综合题###12
        # ## Output
        # 广东省湛江市第二十一中学2024-2025学年高一下学期4月月考地理试题###1
        # 一、单选题###3
        # 二、综合题###12

        # ## Input
        # 第1节 质量###3
        # A未知###4
        # 知识点 1 质量###5
        # 知识点 2 质量的测量###14
        # 知识点 3 质量是物体的一种属性###22
        # B规律方法综合练能力达标###25
        # C创新情境拓展练素养提升###50
        # ## Output
        # 第1节 质量###3
        # A未知###4
        # 知识点 1 质量###5
        # 知识点 2 质量的测量###14
        # 知识点 3 质量是物体的一种属性###22
        # B规律方法综合练能力达标###25
        # C创新情境拓展练素养提升###50

        # # Output
        # 严格按照fewshots输出，不要给其他东西。

        # # Init
        # 请开始你的表演！！！
        # '''

        # model_res, cost_token = deepseek_hs.v3(prompt, temperature=0.1)
        # print(title_str)
        # print('\n\n\n')
        # print(model_res)

        model_res = model_res.replace('```JSON', '').replace('```', '').replace('```json', '').replace('json', '')
        model_res = model_res.replace('true', 'True').replace('false', 'False')

        # 转成 python list
        try:
            json_data = ast.literal_eval(model_res)
        except Exception as e:
            print(f"model_res={model_res}")
            raise VerifyError(f"大模型返回结果结构化失败")

        # 检查 json 结构是否正确
        try:
            for item in json_data:
                is_title, line_num = item['is_title'], item['line_num']
                item['line_num'] = int(line_num)
                isinstance(is_title, bool)
        except Exception as e:
            raise VerifyError(f"{self.task_id} model json data check error，json_data={json_data}")

        title_list = []
        for item in json_data:
            if not item['is_title']:
                continue
            title_list.append(item)
        return title_list, cost_token
