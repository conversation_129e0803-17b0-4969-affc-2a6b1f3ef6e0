# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/2/27 19:27
from app.basic.api import internal_server
from app.basic.log import logger


class TitleFuzzyCheck:

    def __init__(self, title_list, task_id):
        self.title_list = title_list
        self.task_id = task_id

    def main(self):
        logger.info(f"{self.task_id} TitleFuzzyCheck start, before length: {len(self.title_list)}")
        new_title_list = []
        for item in self.title_list:
            title_str = item['title_str']
            is_title_verified = item.get('is_title_verified')
            if is_title_verified:
                new_title_list.append(item)
                continue
            if self.is_title(title_str):
                new_title_list.append(item)
        logger.info(f"{self.task_id} TitleFuzzyCheck success, after length: {len(new_title_list)}")
        return new_title_list

    def is_title(self, title_str):
        res = internal_server.filter_title([title_str])
        if res:
            return True
        return False
