# -*-coding:utf-8 -*-
# Author     ：wang<PERSON>ua
# Email      ：<EMAIL>
# Time       ：2025/2/27 19:26
import re

from app.basic.log import logger


class FilterByCase:

    def __init__(self, title_list, task_id):
        self.title_list = title_list
        self.task_id = task_id

        self.bad_keywords = [
            '(答案)|(解析)|(故选)|(解得)|(答：)|(甲乙)|(如图)|(年级)|(回答)|(测试文本)|(考点：)|(正确的是)|(（分）)|(综上可知)|(题图)',
            '^\s+$',     # 空
            '^\d+$', '^[\d，.,]+$',
            '^\$\$.*?\$\$[.,。，；]*$',
            '.*?而.*?是', '.*?当.*?时', '.*?故.*?为',
            '^(所以)',    # 以这些字符开头
            '[A_Z\.]+',  # 当这一行只有某些字符
            '[：，；。]$',  # 以某些字符结尾
            '\d+\.（\d\s*分）[A-Z]$'
        ]

        self.good_keywords = [
            '(第一章)|(考向\s*\d+)|(专项突破)'
        ]

    def main(self):
        logger.info(f"{self.task_id} FilterByCase before length: {len(self.title_list)}")
        self.filter_badcase()
        self.filter_goodcase()
        logger.info(f"{self.task_id} FilterByCase after length: {len(self.title_list)}")
        return self.title_list

    def filter_badcase(self):
        new_title_list = []
        for item in self.title_list:
            if item['is_title_verified']:
                new_title_list.append(item)
                continue

            title_str = item['title_str']
            if self.is_badcase(title_str):
                continue
            new_title_list.append(item)

        self.title_list = new_title_list

    def filter_goodcase(self):
        for index, item in enumerate(self.title_list):
            title_str = item['title_str']
            if not self.is_goodcase(title_str):
                continue
            # is_title_verified: 审核过，确定为标题
            self.title_list[index]['is_title_verified'] = True

    def is_badcase(self, s):
        for k in self.bad_keywords:
            if re.search(rf'{k}', s):
                # logger.info(f"{s} *** {k}")
                return True
        return False

    def is_goodcase(self, s):
        for k in self.good_keywords:
            if re.search(rf'{k}', s):
                # logger.info(f"{s} *** {k}")
                return True
        return False
