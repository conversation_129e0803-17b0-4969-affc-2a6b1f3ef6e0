# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/24 16:10
import os.path
import re
import fitz
import random
import requests
from urllib.parse import urljoin

from app.basic.db.mongo import mongo_cli
from bson import ObjectId
from app.basic.baseerror import VerifyError
from constants import DATA_BASE_PATH, WORKSHEET_BUCKET, PUBLIC_OSS_URL_ORIGIN
from app.basic.storagehandler import storage


class PdfComment:

    def __init__(self, ticket_id, comment_dict):
        self.ticket_id = ticket_id
        self.comment_dict = comment_dict

    def main(self):
        pdf_info = self.get_pdf_url()
        for pdf_type, item in pdf_info.items():
            pdf_url = item['pdf_url']
            pdf_name = os.path.basename(pdf_url)
            commented_pdf_name = re.sub(r'\.pdf', '_comment.pdf', pdf_name, flags=re.I)
            pdf_path = os.path.join(DATA_BASE_PATH, pdf_name)
            output_pdf_path = os.path.join(DATA_BASE_PATH, commented_pdf_name)

            with open(pdf_path, 'wb') as fp:
                fp.write(requests.get(pdf_url).content)
            self.add_comments_to_pdf(pdf_path, output_pdf_path)
            commented_pdf_url = self.upload_pdf_url(output_pdf_path)
            pdf_info[pdf_type]['commented_pdf_url'] = commented_pdf_url
        return pdf_info

    def get_pdf_url(self):
        ticket_info = mongo_cli['worksheet']['ticket'].find_one(filter={'_id': ObjectId(self.ticket_id)})
        pdf_info = ticket_info['material']['oss_fbd_zips'][0]['fbd_info']
        for _, item in pdf_info.items():
            pdf_url = item['pdf_url']
            response = requests.get(pdf_url)
            if response.status_code != 200:
                raise VerifyError(f"{self.ticket_id} PDF 生成失败")
        return pdf_info

    def add_comments_to_pdf(self, input_pdf, output_pdf):
        """
        在PDF中查找特定模式并在侧边添加批注
        参数:
            input_pdf: 输入PDF文件路径
            output_pdf: 输出PDF文件路径
            pattern: 要查找的正则表达式模式
        """
        # 打开PDF文件
        doc = fitz.open(input_pdf)
        # 编译正则表达式
        regex = re.compile(r'#t\d+#')
        # 遍历每一页
        for page_num in range(len(doc)):
            page = doc[page_num]
            page_rect = page.rect  # 页面矩形区域
            margin = 30  # 边距
            # 计算左右空白区域
            left_rect = fitz.Rect(
                0,  # x0
                0,  # y0
                margin,  # x1 - 左侧空白宽度
                page_rect.height  # y1
            )
            right_rect = fitz.Rect(
                page_rect.width - margin,  # x0
                0,  # y0
                page_rect.width,  # x1
                page_rect.height  # y1
            )
            # 获取页面文本和文本位置信息
            text_instances = page.get_text("dict")["blocks"]

            # 合并相邻文本块
            merged_text_blocks = []
            current_block = None
            for block in text_instances:
                if "lines" in block:  # 确保是文本块
                    text = ''.join([span["text"] for line in block["lines"] for span in line["spans"]])
                    if current_block is None:
                        current_block = block.copy()
                        current_block["text"] = text
                    else:
                        # 这里假设垂直位置相近的文本块为相邻，可根据实际调整
                        prev_bbox = fitz.Rect(current_block["bbox"])
                        current_bbox = fitz.Rect(block["bbox"])
                        if abs(prev_bbox.y1 - current_bbox.y0) < 10:  # 垂直距离小于10视为相邻
                            current_block["text"] += text
                        else:
                            merged_text_blocks.append(current_block)
                            current_block = block.copy()
                            current_block["text"] = text
            if current_block is not None:
                merged_text_blocks.append(current_block)

            # 存储所有匹配项及其位置
            matches_info = []
            # 遍历所有合并后的文本块收集匹配项
            for block in merged_text_blocks:
                text = block["text"]
                print(text)
                # 搜索匹配项
                matches = regex.finditer(text)
                for match in matches:
                    matched_text = match.group()
                    # 这里简单假设匹配文本在块的中心位置，可根据实际情况调整
                    block_bbox = fitz.Rect(block["bbox"])
                    center_x = (block_bbox.x0 + block_bbox.x1) / 2
                    center_y = (block_bbox.y0 + block_bbox.y1) / 2
                    # 调整边界框以仅包含匹配的文本（这里简单处理）
                    match_bbox = fitz.Rect(
                        center_x - 10,
                        center_y - 5,
                        center_x + 10,
                        center_y + 5
                    )
                    matches_info.append({
                        "tag": matched_text,
                        "text": self.comment_dict.get(matched_text, ''),
                        "bbox": match_bbox,
                        "y_pos": (match_bbox.y0 + match_bbox.y1) / 2  # 垂直中心位置
                    })

            # 对匹配项按垂直位置排序
            matches_info.sort(key=lambda x: x["y_pos"])
            # 添加批注
            for i, match in enumerate(matches_info):
                # 随机选择左侧或右侧
                use_left = random.choice([True, False])
                # 计算批注位置
                annot_height = 30  # 批注高度
                annot_width = margin - 10  # 批注宽度
                if use_left:
                    annot_rect = fitz.Rect(
                        left_rect.x0 + 5,  # x0
                        match["y_pos"] - annot_height / 2,  # y0
                        left_rect.x1 - 5,  # x1
                        match["y_pos"] + annot_height / 2  # y1
                    )
                else:
                    annot_rect = fitz.Rect(
                        right_rect.x0 + 5,  # x0
                        match["y_pos"] - annot_height / 2,  # y1
                        right_rect.x1 - 5,  # x1
                        match["y_pos"] + annot_height / 2  # y1
                    )
                # 创建批注
                annot = page.add_text_annot(annot_rect.tl, f"[{i + 1}] {match['text']}")
                # 设置批注样式
                annot.set_border(width=0.5)  # 边框宽度
                annot.set_colors(stroke=(0, 0, 1))  # 蓝色边框
                annot.set_opacity(0.8)  # 透明度
                annot.set_open(True)
                annot.set_info(title=f"{match['tag']}", content=f"批注内容: {match['text']}")
                # 添加从批注到文本的连线
                if use_left:
                    start_point = fitz.Point(annot_rect.x1, annot_rect.y0 + annot_rect.height / 2)
                    end_point = fitz.Point(match["bbox"].x0, match["bbox"].y0 + match["bbox"].height / 2)
                else:
                    start_point = fitz.Point(annot_rect.x0, annot_rect.y0 + annot_rect.height / 2)
                    end_point = fitz.Point(match["bbox"].x1, match["bbox"].y0 + match["bbox"].height / 2)
                line_annot = page.add_line_annot(start_point, end_point)
                line_annot.set_colors(stroke=(0, 0, 1))  # 蓝色连线
                line_annot.set_border(width=0.3)  # 线宽
                line_annot.set_opacity(0.5)  # 透明度
        # 保存修改后的PDF
        doc.save(output_pdf)
        doc.close()
        print(f"处理完成，结果已保存到 {output_pdf}")

    def upload_pdf_url(self, output_pdf_path):
        _, object_name = storage.put_object_from_file(
            bucket_name=WORKSHEET_BUCKET,
            object_name='',
            filename=output_pdf_path
        )
        pdf_url = urljoin(PUBLIC_OSS_URL_ORIGIN, object_name)
        return pdf_url
