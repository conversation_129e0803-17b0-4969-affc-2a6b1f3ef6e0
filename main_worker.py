# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/8 12:38
import time
import argparse
from tornado import web, ioloop
from functools import partial

from app.basic.log import logger
from app.enums.task import TaskType
from run_task import RunTask
import constants


async def main_server():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '-t',
        '--task_type',
        choices=[
            TaskType.XDOC_HTML_FIX.value,
            TaskType.XDOC_HTML_FIX_PRIORITY.value,
            TaskType.TITLE_LEVEL.value,
            TaskType.PDF_COMMENT.value,
            TaskType.JSON_DUPLICATE_CHECK,
        ],
        help='任务类型'
    )

    args = parser.parse_args()
    task_type = args.task_type

    if not task_type:
        raise '-t 参数为必传，请检查！'

    # 清除历史文件
    # utils.delete_file_by_dir(constants.BASE_LOCAL_DIR)

    # start_time 说明
    await RunTask(task_type=task_type).main()


if __name__ == '__main__':
    print(constants.ENV)
    main_with_args = partial(main_server)
    ioloop.PeriodicCallback(main_with_args, 5000).start()  # 定时执行
    ioloop.IOLoop.current().start()
    try:
        main_server()
    except (KeyboardInterrupt, SystemExit) as e:
        logger.info(f'{e.__class__.__name__} received')
