const fs = require('fs');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');

// 启动日志
console.log(`[${new Date().toISOString()}] 下载脚本启动`);
console.log('----------------------------------------');

// 读取JSON文件
console.log('正在读取配置文件 subject_material.json...');
const rawData = fs.readFileSync('./local_scripts/subject_material.json');
const data = JSON.parse(rawData);
console.log(`✅ 配置文件加载完成，共发现 ${Object.keys(data).length} 个学科`);

// 创建data目录
const baseDir = './local_scripts/data';
if (!fs.existsSync(baseDir)) {
    fs.mkdirSync(baseDir);
    console.log(`📁 创建主存储目录: ${path.resolve(baseDir)}`);
}

function adjustMaterialTags() {
    const baseDir = './local_scripts/data';
    const cheerio = require('cheerio');

    fs.readdirSync(baseDir).forEach(subjectDir => {
        const taskPath = path.join(baseDir, subjectDir);
        if (!fs.statSync(taskPath).isDirectory()) return;

        try {
            const inputFile = path.join(taskPath, 'processed.html');
            const outputFile = path.join(taskPath, 'processed2.html');

            const content = fs.readFileSync(inputFile, 'utf8');
            const $ = cheerio.load(content, {
                xmlMode: false,
                recognizeSelfClosing: true
            });

            let modified = false;
            let sectionCount = 0;

            $('hr[data-label="material_start_separator"]').each((i, startHr) => {
                const $start = $(startHr);
                let $current = $start.next();
                let inserted = false;
                const $oldEnd = $start.nextAll('hr[data-label="material_end_separator"]').first();

                while ($current.length && !inserted) {
                    if ($current.is('p') && $current.find('span[data-label="quest_num"]').length) {
                        // 创建带样式的容器
                        const $container = $(`
                            <div style="
                                background: #e8f5e9;
                                padding: 12px;
                                margin: 8px 0;
                                border-left: 3px solid #43a047;
                            "></div>
                        `.replace(/\n\s+/g, ''));

                        // 插入新结束标签
                        const $newEnd = $('<hr data-label="material_end_separator">');
                        $current.before($newEnd);

                        // 包裹内容
                        const $elements = $start.nextUntil($newEnd);
                        if ($elements && $elements.length) { // 添加空值检查
                            $elements.first().before($container);
                            $elements.appendTo($container);

                            // 成功包裹后隐藏标签
                            $start.attr('style', 'display: none !important;');
                            $newEnd.attr('style', 'display: none !important;');
                        } else {
                            console.log(`⚠️ 空材料块：${path.basename(taskPath)} #${i + 1}`);
                            return; // 跳过空块处理
                        }

                        // 清理旧标签
                        $oldEnd.remove();

                        modified = inserted = true;
                        sectionCount++;
                        console.log(`✅ 材料块处理成功：${path.basename(taskPath)} #${i + 1}`);
                    }
                    $current = $current.next();
                }

                if (!inserted) {
                    console.log(`⏩ 未定位题号：${path.basename(taskPath)} #${i + 1}`);
                }
            });

            if (modified) {
                fs.writeFileSync(outputFile, $.html());
                console.log(`🆕 生成文件：${path.relative(baseDir, outputFile)} (${sectionCount} 个材料块)`);
            } else {
                console.log(`⏩ 无修改：${path.basename(taskPath)}`);
            }

        } catch (error) {
            console.error(`❌ 处理失败 [${path.basename(taskPath)}]：${error.message}`);
        }
    });
}

function formatMaterialFile() {
    const baseDir = './local_scripts/data';
    const cheerio = require('cheerio');

    fs.readdirSync(baseDir).forEach(subjectDir => {
        const taskPath = path.join(baseDir, subjectDir);
        if (!fs.statSync(taskPath).isDirectory()) return;

        // 处理processed2.html
        const process2File = path.join(taskPath, 'processed2.html');
        const processContent = fs.readFileSync(process2File, 'utf8');
        const $process = cheerio.load(processContent);

        // 提取材料块
        const processMaterials = [];
        $process('hr[data-label^="material_start_"]').each((i, hr) => {
            const $start = $process(hr);
            const uid = $start.attr('data-label').split('_').pop();
            const $container = $start.nextUntil(`hr[data-label="material_end_separator"]`);
            const html = `
                <div style="
                    background: #e8f5e9;
                    padding: 12px;
                    margin: 8px 0;
                    border-left: 3px solid #43a047;
                ">${$container.html()}</div>
            `.replace(/\n\s+/g, '');
            processMaterials.push(html);
        });

        // 处理machine2.html
        const machine2File = path.join(taskPath, 'machine2.html');
        const machineContent = fs.readFileSync(machine2File, 'utf8');
        const $machine = cheerio.load(machineContent);

        // 提取材料块
        const machineMaterials = [];
        $machine('hr[data-label^="material_start_"]').each((i, hr) => {
            const $start = $machine(hr);
            const $container = $start.nextUntil(`hr[data-label="material_end_separator"]`);
            const html = `
                <div style="
                    background: #e8f5e9;
                    padding: 12px;
                    margin: 8px 0;
                    border-left: 3px solid #43a047;
                ">${$container.html()}</div>
            `.replace(/\n\s+/g, '');
            machineMaterials.push(html);
        });

        // 生成processed3.html
        const process3Content = processMaterials.join('\n');
        fs.writeFileSync(path.join(taskPath, 'processed3.html'), process3Content);

        // 生成machine3.html
        const machine3Content = machineMaterials.join('\n');
        fs.writeFileSync(path.join(taskPath, 'machine3.html'), machine3Content);

        console.log(`✅ 生成材料合并文件：${path.relative(baseDir, taskPath)}`);
    });
}

function statMaterialFile() {
    const baseDir = './local_scripts/data';
    const cheerio = require('cheerio');

    let totalMachineOnly = 0;
    let totalProcessedOnly = 0;
    let totalCommon = 0;
    let totalProcessedTotal = 0;

    try {
        fs.readdirSync(baseDir).forEach(subjectDir => {
            const taskPath = path.join(baseDir, subjectDir);
            if (!fs.statSync(taskPath).isDirectory()) return;

            // // 检查是否到达目标任务
            // if (taskPath.includes('chinese_19855733')) {
            //     throw new Error('已到达目标任务，停止处理');
            // }

            // 读取机器标注材料
            const machineFile = path.join(taskPath, 'machine3.html');
            const machineContent = fs.existsSync(machineFile) ? fs.readFileSync(machineFile, 'utf8') : '';
            const $machine = cheerio.load(machineContent);

            // 读取人工标注材料
            const processedFile = path.join(taskPath, 'processed3.html');
            const processedContent = fs.existsSync(processedFile) ? fs.readFileSync(processedFile, 'utf8') : '';
            const $processed = cheerio.load(processedContent);

            // if (taskPath.includes('physics_19910875')) {
            //     debugger;
            // }

            // 提取材料块和UID
            const extractMaterialBlocks = ($) => {
                const blocks = new Map();
                // 根据实际结构选择材料块容器（当前为带样式的div）
                $('div').each((i, div) => {
                    const $container = $(div);

                    // 提取纯文本并过滤非中英文字符
                    let rawText = $container.text();
                    rawText = rawText.replace(/\$\$[^$]+\$\$/g, 'latex');
                    rawText = rawText
                        .replace(/[^\u4e00-\u9fa5a-zA-Z]/g, '')
                        .toLowerCase();

                    // 生成内容哈希
                    const hash = crypto.createHash('sha256')
                        .update(rawText)
                        .digest('hex')
                        .substring(0, 12);

                    blocks.set(hash, {
                        html: $container.html(),
                        text: rawText
                    });
                });
                return blocks;
            };

            // 获取材料块集合
            const machineBlocks = extractMaterialBlocks($machine);
            const processedBlocks = extractMaterialBlocks($processed);

            // 统计差异
            const onlyMachine = [...machineBlocks].filter(([hash]) => !processedBlocks.has(hash));
            const onlyProcessed = [...processedBlocks].filter(([hash]) => !machineBlocks.has(hash));

            const commonMaterials = new Set([...machineBlocks.keys()].filter(hash => processedBlocks.has(hash)));

            // 生成machine4.html
            if (fs.existsSync(path.join(taskPath, 'machine4.html'))) {
                fs.unlinkSync(path.join(taskPath, 'machine4.html'));
            }
            if (onlyMachine.length > 0) {
                const machine4Content = onlyMachine.map(([_, { html }]) =>
                    `<div style="background:#e3f2fd;padding:12px;margin:8px 0;border-left:3px solid #2196f3">${html}</div>`
                ).join('\n');
                fs.writeFileSync(path.join(taskPath, 'machine4.html'), machine4Content);
            }

            // 生成processed4.html
            if (fs.existsSync(path.join(taskPath, 'processed4.html'))) {
                fs.unlinkSync(path.join(taskPath, 'processed4.html'));
            }
            if (onlyProcessed.length > 0) {
                const processed4Content = onlyProcessed.map(([_, { html }]) =>
                    `<div style="background:#e8f5e9;padding:12px;margin:8px 0;border-left:3px solid #43a047">${html}</div>`
                ).join('\n');
                fs.writeFileSync(path.join(taskPath, 'processed4.html'), processed4Content);
            }

            // 生成match.html
            if (fs.existsSync(path.join(taskPath, 'match.html'))) {
                fs.unlinkSync(path.join(taskPath, 'match.html'));
            }
            if (commonMaterials.size > 0) {
                const commonContent = [...commonMaterials].map(hash => {
                    const machineHtml = machineBlocks.get(hash).html;
                    return `
                        <div style="
                            background: #f3e5f5;
                            padding: 12px;
                            margin: 8px 0;
                            border-left: 3px solid #9c27b0;
                        ">
                            <div style="padding:8px;background:#9c27b0;color:white;margin-bottom:8px">
                                共有材料块 #${Array.from(commonMaterials).indexOf(hash) + 1}
                            </div>
                            ${machineHtml}
                        </div>
                    `.replace(/\n\s+/g, '');
                }).join('\n');

                fs.writeFileSync(path.join(taskPath, 'match.html'), commonContent);
            }

            // 最终统计
            console.log('\n----------------------------------------');
            console.log(`📊 材料统计结果 [${path.basename(taskPath)}]`);
            console.log(`   机器独有材料: ${onlyMachine.length} 个`);
            console.log(`   人工独有材料: ${onlyProcessed.length} 个`);
            console.log(`   共同材料: ${commonMaterials.size} 个`);

            // 累计全局统计
            totalMachineOnly += onlyMachine.length;
            totalProcessedOnly += onlyProcessed.length;
            totalCommon += commonMaterials.size;
            totalProcessedTotal += processedBlocks.size;
        });
    } catch (error) {
        if (error.message.includes('已到达目标任务，停止处理')) {
            console.log('已到达目标任务，停止处理');
        } else {
            console.error(`❌ 统计错误：${error.message}`);
        }
    }

    console.log('\n========================================');
    console.log('🌐 全局统计结果');
    console.log(`   总任务数: ${fs.readdirSync(baseDir).length} 个`);
    console.log(`   总共有材料: ${totalCommon} 个`);
    console.log(`   总人工标注材料: ${totalProcessedTotal} 个`);

    const globalAutomationRate = totalProcessedTotal > 0
        ? (totalCommon / totalProcessedTotal * 100).toFixed(1) + '%'
        : 'N/A';

    const globalMachineTotal = totalMachineOnly + totalCommon;
    const globalFalseRate = globalMachineTotal > 0
        ? (totalMachineOnly / globalMachineTotal * 100).toFixed(1) + '%'
        : 'N/A';

    console.log(`   全局自动化覆盖率: ${globalAutomationRate}`);
    console.log(`   全局机器误标率: ${globalFalseRate}`); // 新增
    console.log('----------------------------------------');
    console.log('说明：自动化覆盖率 = 共同材料数 / 人工标注材料总数');
    console.log('机器误标率 = 机器独有材料数 / (机器独有材料数 + 共同材料数)');
}

function processMachineFile() {
    const baseDir = './local_scripts/data';
    const cheerio = require('cheerio');

    function flushGroups(currentGroup, groups) {
        if (currentGroup.length) {
            groups.push(currentGroup);
            return [];
        }
        return currentGroup;
    }

    function getLabelRecursive($el) {
        const selfLabel = $el.attr('data-label');
        if (selfLabel) return selfLabel;
        const child = $el.children().first();
        return child.length ? getLabelRecursive(child) : '';
    }

    fs.readdirSync(baseDir).forEach(subjectDir => {
        const taskPath = path.join(baseDir, subjectDir);
        if (!fs.statSync(taskPath).isDirectory()) return;

        const inputFile = path.join(taskPath, 'machine.html');
        const content = fs.readFileSync(inputFile, 'utf8');
        const $ = cheerio.load(content);

        // 获取所有直接子元素（兼容无body结构）
        const elements = $(':root').toArray();
        let groups = [];
        let currentGroup = [];

        elements.forEach((el, index) => {
            const $el = $(el);
            const label = getLabelRecursive($el);
            const textContent = $el.text().trim();

            const nodeData = {
                node: el,
                label: label || 'normal',
                text: textContent
            };

            if ($el.is('hr[data-label="material_start_separator"]')) {
                if (currentGroup.length) groups.push(currentGroup);
                currentGroup = [nodeData];
            } else if ($el.is('hr[data-label="material_end_separator"]')) {
                currentGroup.push(nodeData);
                groups.push(currentGroup);
                currentGroup = [];
            } else {
                currentGroup.push(nodeData);
            }

            // 处理最后一个元素
            if (index === elements.length - 1 && currentGroup.length) {
                groups.push(currentGroup);
            }
        });

        groups = groups.filter(g => g.length > 0);
        groups = groups.filter(g => g[0].label === 'material_start_separator' && g[g.length - 1].label === 'material_end_separator');

        groups.forEach(group => {
            // 创建容器和标签
            const $startHr = $(group[0].node);
            const $endHr = $(group[group.length - 1].node);

            // 确保已存在的hr也被隐藏
            $startHr.attr('style', 'display: none !important;');
            $endHr.attr('style', 'display: none !important;');

            const $container = $(`
                <div style="
                    background: #e8f5e9;
                    padding: 12px;
                    margin: 8px 0;
                    border-left: 3px solid #43a047;
                "></div>
            `.replace(/\n\s+/g, ''));

            // 将节点移动到容器中
            group.slice(1, -1).forEach(({ node }) => {
                $container.append(node);
            });

            // 插入容器
            $startHr.after($container);
        });

        console.log('✅ 材料组过滤完成');

        const outputPath = path.join(taskPath, 'machine2.html');
        fs.writeFileSync(outputPath, $.html());
        console.log(`🆕 生成材料文件：${path.relative(baseDir, outputPath)}`);
    });
}

const run = async () => {
    // 重新整理一下人工标注过的html文件，看起来更直观一些
    // adjustMaterialTags();
    // 简单标一下材料
    processMachineFile();
    // 重新整理一下html文件，得到机器标的和人标的材料
    formatMaterialFile();
    // 统计一下数据，看看机器标&人没标、机器没标&人标，分别有多少，是什么
    statMaterialFile();
};
run();