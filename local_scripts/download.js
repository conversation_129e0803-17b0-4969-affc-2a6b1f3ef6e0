const fs = require('fs');
const path = require('path');
const axios = require('axios');
const crypto = require('crypto');

// 启动日志
console.log(`[${new Date().toISOString()}] 下载脚本启动`);
console.log('----------------------------------------');

// 读取JSON文件
console.log('正在读取配置文件 subject_material.json...');
const rawData = fs.readFileSync('./local_scripts/subject_material.json');
const data = JSON.parse(rawData);
console.log(`✅ 配置文件加载完成，共发现 ${Object.keys(data).length} 个学科`);

// 创建data目录
const baseDir = './local_scripts/data';
if (!fs.existsSync(baseDir)) {
    fs.mkdirSync(baseDir);
    console.log(`📁 创建主存储目录: ${path.resolve(baseDir)}`);
}

// 下载函数
async function downloadTaskFiles() {
    let totalTasks = 0;
    const startTime = Date.now();

    // 遍历所有学科分类
    for (const [subject, tasks] of Object.entries(data)) {
        console.log(`\n📚 开始处理 ${subject} 学科 (${tasks.length} 个任务)`);

        for (const task of tasks) {
            totalTasks++;
            const taskDir = path.join(baseDir, `${subject}_${task.task_id}`);

            // 创建任务目录
            if (!fs.existsSync(taskDir)) {
                fs.mkdirSync(taskDir, { recursive: true });
                console.log(`🆕 创建任务目录: ${taskDir}`);
            }

            try {
                console.log(`🔄 正在处理任务 ${subject}_${task.task_id}...`);
                const taskStart = Date.now();

                // 下载machine.html
                console.log(`⬇️  开始下载 machine.html`);
                const machineResponse = await axios.get(task.machine_url);
                fs.writeFileSync(path.join(taskDir, 'machine.html'), machineResponse.data);

                // 下载processed.html
                console.log(`⬇️  开始下载 processed.html`);
                const processedResponse = await axios.get(task.html_url);
                fs.writeFileSync(path.join(taskDir, 'processed.html'), processedResponse.data);

                // 下载统计
                const duration = (Date.now() - taskStart) / 1000;
                console.log(`✅ 下载完成 [耗时 ${duration}s]`);
                console.log(`   machine.html 大小: ${Math.round(machineResponse.data.length / 1024)}KB`);
                console.log(`   processed.html 大小: ${Math.round(processedResponse.data.length / 1024)}KB`);

            } catch (error) {
                console.error(`❌ 下载失败: ${error.message}`);
                console.log('⏩ 跳过当前任务，继续执行...');
            }
        }
    }

    // 最终统计
    console.log('\n----------------------------------------');
    console.log(`✅ 全部任务处理完成！`);
    console.log(`   总处理任务数: ${totalTasks} 个`);
    console.log(`   总耗时: ${(Date.now() - startTime) / 1000} 秒`);
}

const run = async () => {
    // 下载word_html和人工标注过的html文件
    // await downloadTaskFiles();
};
run();