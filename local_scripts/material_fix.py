import os
import shutil
import re
import httpx
import asyncio
from bs4 import BeautifulSoup
from volcenginesdkarkruntime import Ark, AsyncArk


DEFAULT_TEMPERATURE = 1

MODEL_MAPPING = {
    'volcengine': {
        'deepseek_v3': 'deepseek-v3-250324',
        'deepseek_r1': 'deepseek-r1-250120',
        'doubao_32k': 'doubao-1-5-pro-32k-250115',
        'doubao_256k': 'doubao-1-5-pro-256k-250115'
    },
    'siliconflow': {
        'deepseek_v3': 'deepseek-ai/DeepSeek-V3',
        'deepseek_r1': 'deepseek-ai/DeepSeek-R1',  
    },
    'deepseek': {
        'deepseek_v3': 'deepseek-chat',
        'deepseek_r1': 'deepseek-reasoner'
    }
}

# 1=volcengine, 2=siliconflow, 3=deepseek
MODEL_SERVICE = 2  # 默认使用volcengine


def backup_machine_files():
    base_dir = os.path.join('local_scripts', 'data')
    
    # 遍历学科目录
    for subject_dir in os.listdir(base_dir):
        task_path = os.path.join(base_dir, subject_dir)
        
        if not os.path.isdir(task_path):
            continue
            
        # 定义源文件和目标文件路径
        src_file = os.path.join(task_path, 'machine.html')
        dst_file = os.path.join(task_path, 'machine.bak.html')
        
        try:
            # 执行文件备份
            if os.path.exists(src_file):
                shutil.copy2(src_file, dst_file)
                print(f"✅ 备份成功：{subject_dir}")
            else:
                print(f"⚠️  文件不存在：{subject_dir}/machine.html")
                
        except Exception as e:
            print(f"❌ 备份失败 [{subject_dir}]: {str(e)}")


async def run():
    """遍历目录处理所有machine.html"""
    base_dir = os.path.join('local_scripts', 'data')
    subject_dirs = [d for d in os.listdir(base_dir) if os.path.isdir(os.path.join(base_dir, d))]
    total = len(subject_dirs)

    start_processing = False
    
    for i, subject_dir in enumerate(subject_dirs, 1):
        # 从目录名提取学科名称（如：chinese_19855733 -> chinese）
        subject = subject_dir.split('_')[0].lower()
        task_path = os.path.join(base_dir, subject_dir)
        print(f"\n处理进度: {i}/{total} ({subject} - {subject_dir})")

        # # 检查是否到达目标目录
        # if 'chinese_19855733' in subject_dir:
        #     start_processing = True
            
        # if not start_processing:
        #     print(f"⏩ 跳过目录: {subject_dir}")
        #     continue
        
        input_file = os.path.join(task_path, 'machine.bak.html')
        output_file = os.path.join(task_path, 'machine.html')

        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                result, time, token = await material_fix(f.read(), subject)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(result)
            
            print(f"✅ 处理完成：{subject_dir}, 耗时: {time:.2f}s, token: {token}")
            
        except Exception as e:
            print(f"❌ 处理失败 [{subject_dir}]: {str(e)}")


async def run2():
    """定时任务入口函数"""
    from apscheduler.schedulers.asyncio import AsyncIOScheduler
    from apscheduler.triggers.cron import CronTrigger
    
    scheduler = AsyncIOScheduler()
    # 每天凌晨1点执行（服务器时区）
    trigger = CronTrigger(hour=1, minute=0, second=0)
    
    scheduler.add_job(
        run,
        trigger=trigger,
        max_instances=1,
        misfire_grace_time=3600
    )
    
    scheduler.start()
    print("✅ 定时任务已启动，每天凌晨1点自动运行")
    
    try:
        # 保持事件循环运行
        while True:
            await asyncio.sleep(3600)
    except (KeyboardInterrupt, SystemExit):
        scheduler.shutdown()


# main
# @params scene：用于区分不同场景，wps_plugin
async def material_fix(html_content, subject='', model_service=MODEL_SERVICE, scene=''):
    """主处理函数，分两个阶段处理HTML内容
    
    处理流程:
    1. material_fix1: 使用AI模型识别材料范围
    2. material_fix2: 基于规则系统进行后处理
    3. material_fix3: 把材料的范围移动到所有小题后面
    
    参数:
        html_content: str - 需要处理的原始HTML内容
        
    返回值:
        str - 处理后的HTML字符串
    """
    import time
    time_stats = {}

    start = time.time()
    html_content, token1 = await material_fix1(html_content, subject, model_service, scene)
    time_stats['phase1'] = time.time() - start

    start = time.time()
    html_content = material_fix2(html_content, scene)
    time_stats['phase2'] = time.time() - start

    start = time.time()
    html_content, token3 = await material_fix3(html_content, subject, model_service, scene)
    time_stats['phase3'] = time.time() - start

    time_stats['total'] = sum(time_stats.values())
    print(f"处理耗时: {time_stats}")

    return html_content, time_stats['total'], token1 + token3


def format_html(html):
    html = re.sub(r'<img[^>]+>', '#图片#', html) # 图片
    html = re.sub(r'<[^>]+data-label="blank"[^>]+>\s*<\/span>', '#blank#', html) # blank
    
    """表格的内容尽量保留，可能会影响材料范围的标定"""
    if re.search(r'table>', html):
        table_soup = BeautifulSoup(html, 'html.parser')
        cells = [td.get_text(strip=True) for td in table_soup.find_all(['td', 'th'])]
        html = '\n'.join(cells)
    
    html = re.sub(r'<[^>]+>', '', html)
    html = html.replace('<p>', '\r\n').replace('</p>', '')
    return html.strip()


async def volcengine(prompt, model, temperature=DEFAULT_TEMPERATURE):
    client = AsyncArk(
    api_key='c2511de8-f515-4155-aeac-faafaab7062f',
    timeout=httpx.Timeout(timeout=1800)
)
    completion = await client.chat.completions.create(
        model=model,
        messages=[{ "role": "user", "content": prompt }],
        temperature=temperature,
    )
    res = completion.choices[0].message.content
    total_tokens = completion.usage.total_tokens
    return res, total_tokens


async def siliconflow(prompt, model, temperature=DEFAULT_TEMPERATURE):
    """调用硅基流动模型API处理提示文本
    
    参数:
        prompt: str - 需要处理的提示文本
        model: str - 使用的模型名称
        
    返回值:
        tuple - (模型响应内容, 使用的token数量)
    """
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-bgmgjsfwzkiczpcnrrotmmxdydtridozsehajdrugfavcnnm'
    }
    
    data = {
        'model': model,
        'messages': [{'role': 'user', 'content': prompt}],
        'temperature': temperature,
    }
    
    max_retries = 59
    retry_delay = 3  # seconds

    for attempt in range(max_retries):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    'https://api.siliconflow.cn/v1/chat/completions',
                    headers=headers,
                    json=data,
                    timeout=1800
                )
            response.raise_for_status()
            result = response.json()
            
            res = result['choices'][0]['message']['content']
            total_tokens = result['usage']['total_tokens']
            return res, total_tokens
            
        except httpx.HTTPStatusError as e:
            if e.response.status_code == 429:
                if attempt < max_retries - 1:
                    wait_time = retry_delay * (2 ** attempt)
                    print(f"遇到429错误，将在{wait_time}秒后重试 (剩余重试次数: {max_retries - attempt - 1})")
                    await asyncio.sleep(wait_time)
                    continue
            raise


async def deepseek(prompt, model, temperature=DEFAULT_TEMPERATURE):
    """调用DeepSeek模型API处理提示文本
    
    参数:
        prompt: str - 需要处理的提示文本
        model: str - 使用的模型名称
        
    返回值:
        tuple - (模型响应内容, 使用的token数量)
    """
    headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-93422fc9d0494b45916df9a7597cec77'
    }
    
    data = {
        'model': model,
        'messages': [{'role': 'user', 'content': prompt}],
        'temperature': temperature,
    }
    
    async with httpx.AsyncClient() as client:
        response = await client.post(
            'https://api.deepseek.com/chat/completions',
            headers=headers,
            json=data,
            timeout=1800
        )
        response.raise_for_status()
        result = response.json()
        
        res = result['choices'][0]['message']['content']
        total_tokens = result['usage']['total_tokens']
        return res, total_tokens


def handle_rule_filter(groups, scene):
    # - 明显像标题的东西：四、实验题
    # - 明显像答案/解析的东西
    material_groups = []

    for group in groups:
        content = '\n'.join([item['text'] for item in group])

        # - 针对wps场景，我们认为内容顶部的连续内容是材料
        if scene == 'wps_plugin':
            if not group[0]['node'].find_previous_sibling() and \
                re.search(r'data-label="quest_num"', str(group[-1]['node'].find_next_sibling())) and \
                re.search(r'data-level="1"', str(group[-1]['node'].find_next_sibling())):
                material_groups.append(group)
                continue

        if re.search(r'^[一二三四五六七八九十]、', content):
            continue
        # - 可能存在文言文/翻译里的“【甲乙丙丁】”
        if re.search(r'^【.{2}】', content):
            continue
        material_groups.append(group)
    
    return material_groups


# @description：把材料结束符移动到小题后面。
# @todo：可能把不是小题的其他试题包裹进来。
async def material_fix3(html_content, subject, model_service, scene):
    soup = BeautifulSoup(html_content, 'html.parser')
    
    def _handle_groups(soup):
        groups = []
        # 查找所有材料开始标记
        start_hrs = soup.find_all('hr', {'data-label': 'material_start_separator'})
        
        for start_hr in start_hrs:
            material = []
            children = []
            current_node = start_hr.next_sibling
            in_material = True
            
            # 遍历直到找到结束标记
            while current_node:
                if current_node.name == 'hr' and current_node.get('data-label') == 'material_end_separator':
                    in_material = False
                    # 收集结束标记后的内容
                    current_node = current_node.next_sibling
                    while current_node:
                        # 遇到标题标签停止收集
                        if current_node.get('data-label') == 'header':
                            break
                        # 遇到其他分割线停止收集
                        if current_node.name == 'hr':
                            break
                        children.append(current_node)
                        current_node = current_node.next_sibling
                    break
                
                if in_material:
                    material.append(current_node)

                current_node = current_node.next_sibling
            
            if material:
                groups.append({
                    'material': material,
                    'children': children
                })
         
        return groups


    def _get_prompt(groups):
        for item in groups:
            material = item['material']
            material_text = '\r\n'.join([format_html(str(m)) for m in material])
            children = item['children']
            children = [c for c in children if
                        re.search(r'data-label="quest_num"', str(c)) and
                        re.search(r'data-level="1"', str(c)) and
                        not re.search(r'answer|explanation|extra', str(c))]
            item['children_'] = children
            children_text = [format_html(str(c)) for c in children]
            children_text = [f'#p{i+1}#{c}' for i, c in enumerate(children_text)]
            children_text = '\r\n'.join(children_text)
            
            prompt = f'''
            ##background
            我在根据一段材料做题，有一些与材料无关的内容混杂在里面，会影响我的做题效率，请帮我判断一下从第几题开始跟材料关系不大，我就可以忽略掉无关内容。

            ##fewshots
            ###input
            \\'\\'\\'材料
            ......材料的具体内容
            \\'\\'\\'
            \\'\\'\\'试题
            #p1#......与材料相关的试题内容
            #p2#......与材料相关的试题内容
            #p3#......与材料无关的试题内容
            \\'\\'\\'
            ###output
            p3

            ###input
            \\'\\'\\'材料
            ......材料的具体内容
            \\'\\'\\'
            \\'\\'\\'试题
            #p1#......与材料无关的试题内容
            #p2#......与材料无关的试题内容
            #p3#......与材料无关的试题内容
            \\'\\'\\'
            ###output
            p1

            ###input
            \\'\\'\\'材料
            ......材料的具体内容
            \\'\\'\\'
            \\'\\'\\'试题
            #p1#......与材料相关的试题内容
            #p2#......与材料相关的试题内容
            #p3#......与材料相关的试题内容
            \\'\\'\\'
            ###output
            p4

            ###input
            \\'\\'\\'材料
            ......材料的具体内容
            \\'\\'\\'
            \\'\\'\\'试题
            #p1#......与材料相关的试题答案
            #p2#......与材料相关的试题解析
            #p3#......与材料相关的试题解析
            \\'\\'\\'
            ###output
            p4

            ##rules
            1.每行开始的#p\\d+#表示该行的行号，具有唯一性
            2.只有当出现全新无关问题时才返回行号
            3.若当前试题都与材料相关，则返回试题结束后的下一个行号
            4.试题的答案/解析/补充说明/相关知识点也属于与材料相关的内容，不要在这里拆开

            ##init
            请帮我判断一下从第几题开始跟材料关系不大，返回格式p\d+的行号即可。
            \\'\\'\\'材料
            {material_text}
            \\'\\'\\'
            \\'\\'\\'试题
            {children_text}
            \\'\\'\\'
            '''
            prompt = re.sub(r'\n\s+', '\r\n', prompt)
            item['prompt'] = prompt

        return groups


    async def _post_model(groups):
        async def _model(prompt):
            async def postmodel(prompt, model):
                if model_service == 1:
                    model = MODEL_MAPPING['volcengine'].get(model)
                    return await volcengine(prompt, model)
                elif model_service == 2:
                    model = MODEL_MAPPING['siliconflow'].get(model)
                    return await siliconflow(prompt, model)
                elif model_service == 3:
                    model = MODEL_MAPPING['deepseek'].get(model)
                    return await deepseek(prompt, model)


            r1 = await postmodel(prompt, 'deepseek_v3')
            return r1
        

        async def _process_item(item):
            has_option = any(re.search(r'data-label="choice_option"', str(q)) for q in item['children'])
            has_blank = any(re.search(r'data-label="blank"', str(q)) for q in item['children'])

            # - 英语的材料题不需要在试题#选择题、填空题间切割
            if subject == 'english' and (has_option or has_blank):
                return f'#p{len(item["children"]) + 1}#', 0

            """处理单个item的辅助函数"""
            res, total_tokens = await _model(item['prompt'])
            return res, total_tokens


        results = await asyncio.gather(
            *[_process_item(item) for item in groups],
            return_exceptions=True
        )

        for item, (res, total_tokens) in zip(groups, results):
            if isinstance(res, Exception):
                print(f"❌ 处理失败: {str(res)}")
                continue

            item['model_output'] = res
            item['total_tokens'] = total_tokens
            if re.search(r'p\d+', res): item['target_line'] = int(re.search(r'p(\d+)', res).group(1))

        return groups


    def _handle_mark_material(groups):
        for item in groups:
            if not item.get('target_line'): continue
            target_line = item['target_line']
            material = item['material']
            children = item['children'] # 全部childrne
            children_ = item['children_'] # 全部题干childrne
            
            # - 若结果是p1，说明材料标错，取消掉
            # - 否则，把材料结束移动到正确的位置
            if target_line == 1:
                # 移除材料分割线
                if material and material[0].find_previous('hr', {'data-label': 'material_start_separator'}):
                    material[0].find_previous('hr').decompose()
                if material and material[-1].find_next('hr', {'data-label': 'material_end_separator'}):
                    material[-1].find_next('hr').decompose()
                continue

            if 1 < target_line <= len(children_):
                # 移除旧的分割线
                if material and material[-1].find_next('hr', {'data-label': 'material_end_separator'}):
                    material[-1].find_next('hr').decompose()
                # 插入新分割线
                new_end = soup.new_tag('hr', **{'data-label': 'material_end_separator'})
                children_[target_line-1].insert_before(new_end)
                continue
                
            # 移除旧的分割线
            if material and material[-1].find_next('hr', {'data-label': 'material_end_separator'}):
                material[-1].find_next('hr').decompose()
            # 插入新分割线
            new_end = soup.new_tag('hr', **{'data-label': 'material_end_separator'})
            children[-1].insert_after(new_end)

        return groups


    groups = _handle_groups(soup)
    groups = _get_prompt(groups)
    groups = await _post_model(groups)
    groups = _handle_mark_material(groups)

    total_tokens = sum(item.get('total_tokens', 0) for item in groups)
    return str(soup), total_tokens


def material_fix2(html_content, scene):
    """规则处理阶段 - 基于业务规则进行后处理
    
    核心功能:
    1. 校验AI标记的合理性
    2. 应用业务规则调整标记
    3. 清理无效标记
    4. 标准化输出格式
    
    参数:
        html_content: str - 经过AI初步处理的HTML内容
        
    返回值:
        str - 最终处理后的HTML内容
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 获取所有直接子元素
    elements = soup.find_all(recursive=False)
    groups = []
    current_group = []
    
    def get_label_recursive(el):
        if not hasattr(el, 'attrs'):  # 先检查是否有attrs属性
            return ''
        if el.has_attr('data-label'):
            return el['data-label']
        if hasattr(el, 'children'):
            for child in el.children:
                if hasattr(child, 'attrs'):  # 只处理有attrs属性的子元素
                    return get_label_recursive(child)
        return ''

    
    for el in elements:
        label = get_label_recursive(el)
        text_content = el.get_text().strip()
        
        node_data = {
            'node': el,
            'label': label or 'normal',
            'text': text_content
        }
        
        # 处理hr分割线
        if el.name == 'hr':
            if current_group:
                groups.append(current_group)
                current_group = []
            continue
            
        # 处理分组逻辑
        if label == 'header':  # 如果是标题标签
            if current_group: groups.append(current_group)  # 将当前组添加到总组列表
            groups.append([node_data])  # 将标题作为新组单独添加
            current_group = []  # 重置当前组
            continue

        if label == 'quest_num':  # 如果是题号标签
            if current_group: groups.append(current_group)  # 将当前组添加到总组列表
            current_group = [node_data]  # 创建以题号开头的新组
            continue

        if label in ['answer', 'explanation', 'extra']:  # 如果是答案/解析/额外内容标签
            if current_group and current_group[0]['label'] in ['answer', 'explanation', 'extra']:  # 如果当前组也是同类标签
                current_group.append(node_data)  # 添加到当前组
                continue
            # 如果不是同类标签
            if current_group: groups.append(current_group)  # 将当前组添加到总组列表
            current_group = [node_data]  # 创建新组
            continue

        # 其他类型标签
        if current_group and any(item['label'] in ['answer', 'explanation', 'extra'] for item in current_group):  # 如果当前组包含答案/解析类内容
            if current_group: groups.append(current_group)  # 将当前组添加到总组列表
            current_group = []  # 重置当前组
        current_group.append(node_data)  # 将节点添加到当前组
    
    if current_group:
        groups.append(current_group)
    
    # 过滤空组
    groups = [g for g in groups if g]
    
    # 材料过滤逻辑
    material_groups = []
    for i, group in enumerate(groups):
        # 跳过包含hr的组
        if any(item['node'].name == 'hr' for item in group):
            continue
            
        next_group = groups[i+1] if i+1 < len(groups) else None
        
        # 判断是否为材料块的条件
        if not next_group:
            if any('blank' in str(item['node']) for item in group):
                material_groups.append(group)
        elif (next_group[0]['label'] == 'quest_num' and 
              not any(item['label'] in ['answer', 'explanation', 'extra'] for item in next_group)):
            material_groups.append(group)
        elif any('blank' in str(item['node']) for item in group):
            material_groups.append(group)
    
    # 进一步过滤
    material_groups = [
        g for g in material_groups 
        if not any(item['label'] in ['header', 'quest_num', 'answer', 'explanation', 'extra'] for item in g)
    ]
    
    # 过滤特定文本模式
    material_groups = [
        g for g in material_groups 
        if not any(re.search(r'学校[:：]\s*姓名[:：]\s*班级[:：]\s*考号[:：]\s*', item['text']) for item in g)
    ]

    # 基于正则加一些拒绝策略
    material_groups = handle_rule_filter(material_groups, scene)
    
    # 为材料块添加标记
    for group in material_groups:
        first_node = group[0]['node']
        last_node = group[-1]['node']
        
        # 检查前面是否已有material_start_separator
        prev_sibling = first_node.previous_sibling
        has_start_hr = (prev_sibling and prev_sibling.name == 'hr' and 
                        prev_sibling.get('data-label') == 'material_start_separator')
        
        # 检查后面是否已有material_end_separator
        next_sibling = last_node.next_sibling
        has_end_hr = (next_sibling and next_sibling.name == 'hr' and 
                    next_sibling.get('data-label') == 'material_end_separator')
        
        # 只有不存在时才插入
        if not has_start_hr:
            start_hr = soup.new_tag('hr', **{'data-label': 'material_start_separator'})
            first_node.insert_before(start_hr)
        
        if not has_end_hr:
            end_hr = soup.new_tag('hr', **{'data-label': 'material_end_separator'})
            last_node.insert_after(end_hr)
 
    return str(soup)


async def material_fix1(html_content, subject, model_service, scene):
    """AI模型处理阶段 - 使用深度学习模型识别材料范围
    
    核心功能:
    1. 解析HTML结构并分组
    2. 生成模型提示词
    3. 调用AI模型判断材料起始位置
    4. 标记材料范围
    
    参数:
        html_content: str - 原始HTML内容
        
    返回值:
        str - 包含AI标记的HTML内容
    """

    def _handle_groups(html_content):
        """处理HTML内容并分组
        
        参数:
            html_content: str - 原始HTML内容
            
        返回值:
            list - 分组后的材料数据
            BeautifulSoup - 解析后的HTML对象
            
        处理逻辑:
            1. 使用BeautifulSoup解析HTML
            2. 根据分隔符规则进行分组
            3. 构建节点数据结构
        """
        soup = BeautifulSoup(html_content, 'html.parser')
        groups = []
        current_group = []
        separators = [
            # {
            #     'type': 'header',
            #     'condition': lambda tag: (
            #         tag.get('data-label') == 'header' or 
            #         any(child.get('data-label') == 'header' 
            #             for child in tag.find_all(True, recursive=False))
            #     )
            # },
            {
                'type': 'quest_num',
                'condition': lambda tag: (
                    tag.get('data-label') == 'quest_num' 
                    and tag.get('data-level') == '1'
                ) or (any(
                    child.get('data-label') == 'quest_num' 
                    and child.get('data-level') == '1'
                    for child in tag.find_all(True, recursive=False)
                ) and not tag.get('data-label') in ['answer', 'explanation', 'extra'])
            },
            # {
            #     'type': 'quest_num',
            #     'condition': lambda tag: (
            #         tag.name == 'table' and 
            #         len(tag.find_all('span', attrs={'data-label': 'blank'})) >= 2
            #     )
            # },
        ]

        for element in soup.find_all(True, recursive=False):
            is_separator = False
            sep_type = None
            
            # 匹配分割线策略
            for sep in separators:
                if sep['condition'](element):
                    is_separator = True
                    sep_type = sep['type']
                    break

            # 构建节点数据
            node_data = {
                'label': sep_type if is_separator else element.name,
                'node': element,
                'text': element.get_text(strip=True),
                'html': str(element),
                # 修改level获取逻辑（从data-level属性获取）
                'level': int(element.get('data-level')) if is_separator and element.get('data-level') else None,
            }

            if is_separator:
                if current_group:
                    current_group.append(node_data)
                    groups.append(current_group)
                    current_group = []
                current_group = [node_data]
            elif current_group and element.name in ['p', 'table']:
                current_group.append(node_data)
        
        if current_group:
            groups.append(current_group)
        
        # 过滤掉group前面连续的header节点
        filtered_groups = []

        for group in groups:
            # 跳过空组
            if not group: continue
                
            # 找到第一个非header节点的索引
            i = 0
            while i < len(group) and group[i]['label'] == 'header': i += 1
                
            # 如果找到非header节点，保留剩余部分
            if i < len(group): filtered_groups.append(group[i:])
        
        groups = filtered_groups

        def _filter_material_groups(groups):
            """筛选可能包含材料的组"""
            material_groups = []
            # - 包含楷体
            # - 包含单行的加粗/居中文本，它有可能是标题，这里依赖前置标题修复环节
            # - 包含过长的解析文本，它有可能是把材料融到上一题的解析里
            # - 匹配某些固定的模式，e.g. 阅读、材料，@todo：扫一下材料的一些特征补充进来
            # - 若存在连续的label相同的节点，节点里不包含题号，内容比较长，则可能是材料
            for group in groups:
                is_material = False

                # - 若以标题结尾，则排除掉
                if group[-1]['label'] != 'quest_num':
                    continue

                for item in group:
                    # - 包含楷体
                    if 'font-family:楷体' in item['html'] or 'font-family: 楷体' in item['html']:
                        is_material = True
                        break
                    
                    # - 单行加粗/居中文本
                    if (item['label'] == 'p' and 
                        ('<b>' in item['html'] or 
                        '<strong>' in item['html'] or 
                        'text-align:center' in item['html']) and
                        len(item['text']) < 30):
                        is_material = True
                        break
                    
                    # - 超长文本（超过500字符）常见于英语材料
                    if len(item['text']) > 500:
                        is_material = True
                        break
                    
                    # - 匹配固定模式
                    if re.search(r'^(阅读|材料)', item['text']):
                        is_material = True
                        break
                    
                    rules = [
                        '(阅读|根据).{0,10}(材料|短文)',
                        '(完成|回答).{0,10}小题',
                        '据图回答',
                        '请选择',
                        '.+[:：]$',
                    ]
                    regexp = f'{"|".join(rules)}'
                    if re.search(regexp, item['text']):
                        is_material = True
                        break
                    
                def _get_label(item):
                    if hasattr(item['node'], 'attrs') and 'data-label' in item['node'].attrs:
                        return item['node'].attrs['data-label']
                    return ''

                if is_material:
                    material_groups.append(group)
                    continue
                
                # - 若存在连续的label相同的节点，节点里不包含题号，内容比较长，则可能是材料
                _groups = []
                _group = []
                for item in group:
                    label = _get_label(item)

                    if not _group:
                        _group.append(item)
                        continue
                    
                    has_qnum = re.search(r'data-label="quest_num"', str(item['node'])) 
                    
                    # 比较当前节点label与当前组的第一个节点label
                    if _get_label(_group[0]) == label and \
                        re.search(r'data-label="quest_num"', str(_group[0]['node'])) == has_qnum:
                        _group.append(item)
                        continue
                    
                    _groups.append(_group)
                    _group = [item]
                
                for _group in _groups:
                    if not _get_label(_group[0]) in ['explanation', '']:
                        continue
                    total_length = sum(len(item['text']) for item in _group)
                    if total_length < 1000:
                        continue
                    has_qnum = any(
                        re.search(r'data-label="quest_num"', str(item['node'])) 
                        for item in _group 
                        if item['node'].name == 'p'
                    )
                    if has_qnum:
                        continue
                    is_material = True

                if is_material:
                    material_groups.append(group)
                    continue
            
            return material_groups
        

        groups = _filter_material_groups(groups)
        return [groups, soup]


    def _handle_token(groups):
        """生成token文本用于模型输入
        
        参数:
            groups: list - 原始分组数据
            
        处理逻辑:
            1. 拼接文本内容
            2. 处理图片和空白标记
            3. 保留表格内容
            4. 添加行号标记
        """
        new_groups = []
        
        for group in groups:
            # 拼接text字段
            combined_text = '\r\n'.join([item['text'] for item in group])
            
            # 生成token处理逻辑
            token_builder = []
            for idx, item in enumerate(group, start=1):
                html = item['html']
                html = format_html(html)
                
                html = f'#p{idx}#{html}'

                # 拼接处理后的HTML
                token_builder.append(html)

            # 组合token并清理多余换行
            combined_token = '\r\n'.join(token_builder).strip()
            
            # 构建新数据结构
            new_groups.append({
                'token': combined_token,
                'groups': group,
                'text': combined_text,
            })
        
        return new_groups
    

    def _get_prompt(groups):
        """为每个材料组生成提示文本
        
        参数:
            groups: list - 包含材料分组的列表
            
        处理逻辑:
            1. 提取每组最后一道题的文本作为问题描述
            2. 生成标准化的提示模板
            3. 清理多余空白字符
        """
        for item in groups:
            quest_text = item['groups'][-1]['text']
            prompt = f'''
                ##background：
                \\'\\'\\'input
                {item['token']}
                \\'\\'\\'
                我现在在做题，阅读到#{len(item['groups'])}#行：
                \\'\\'\\'
                #{len(item['groups'])}#{quest_text}
                \\'\\'\\'
                上文里掺杂着其他题目的内容，请告诉我该从哪一行开始阅读、是我正在做的试题的相关材料：

                ##definition
                对材料的范围做一些定义：
                1.材料有明确的起始标记，或者材料前面有明确的内容分隔标记
                2.材料可能是文言文/作文，可能包含标题+作者+正文+注释
                材料不包含：
                1.其他试题的答案/解析/译文

                ##rules
                请注意：
                1.每行开始的#p\\d+#表示该行的行号，具有唯一性
                2.若当前行是**明确的材料起始标记**（如“材料一：”等），即使其本身无实质内容，也应返回当前行作为材料起始行
                3.若当前行起到**分隔不同题型/内容模块的作用**（如“阅读材料”、“根据短文内容”等），即使其本身无实质内容，也应返回当前行作为输出
                4.若当前行是**明确的材料起始标记**（如“材料一：”等），或起到**分隔不同题型/内容模块的作用**（如“阅读材料：”等），即使其本身无实质内容，也应返回当前行作为输出
                5.当材料起始标记与题型分隔标记同时存在时，必须必须必须优先认为题型分隔标记的行是期望的输出

                ##fewshots
                ###input
                #p1#......一些其他内容
                #p2#阅读下列材料，完成下面小题。
                #p3#材料一：
                #p4#......具体的材料内容
                ###output
                p2

                ###input
                #p1#......一些其他内容
                #p2#材料一：
                #p3#......具体的材料内容
                ###output
                p2

                ###input
                #p1#......一些其他内容
                #p2#......材料内容
                #p3#20、...试题内容
                #p4#21、...试题内容
                ###output
                p4

                ##init
                只需要返回目标行号即可，即返回结果要严格匹配模式/^p\\d+$/，请回答。
            '''
            prompt = re.sub(r'\n\s+', '\r\n', prompt)
            item['prompt'] = prompt
        
        return groups
    

    async def _post_model(groups):
        """调用模型API处理提示文本
        
        参数:
            groups: list - 包含提示信息的材料组列表
            
        处理逻辑:
            1. 使用交叉验证机制调用不同模型
            2. 解析模型返回结果
            3. 记录token使用情况
        """
        use_cross_validate = False

        async def _model(prompt):
            async def postmodel(prompt, model):
                MODEL_MAPPING = {
                    'volcengine': {
                        'deepseek_v3': 'deepseek-v3-250324',
                        'deepseek_r1': 'deepseek-r1-250120',
                        'doubao_32k': 'doubao-1-5-pro-32k-250115',
                        'doubao_256k': 'doubao-1-5-pro-256k-250115'
                    },
                    'siliconflow': {
                        'deepseek_v3': 'deepseek-ai/DeepSeek-V3',
                        'deepseek_r1': 'deepseek-ai/DeepSeek-R1',  
                    },
                    'deepseek': {
                        'deepseek_v3': 'deepseek-chat',
                        'deepseek_r1': 'deepseek-reasoner'
                    }
                }
                if model_service == 1:
                    model = MODEL_MAPPING['volcengine'].get(model)
                    return await volcengine(prompt, model)
                elif model_service == 2:
                    model = MODEL_MAPPING['siliconflow'].get(model)
                    return await siliconflow(prompt, model)
                elif model_service == 3:
                    model = MODEL_MAPPING['deepseek'].get(model)
                    return await deepseek(prompt, model)


            r1 = await postmodel(prompt, 'deepseek_v3')
            if not use_cross_validate: return r1
            
            """交叉验证"""
            r2 = await postmodel(prompt, 'doubao_32k')
            if re.search(r'p\d+', r1[0]) and \
                re.search(r'p\d+', r2[0]) and \
                int(re.search(r'p(\d+)', r1[0]).group(1)) == int(re.search(r'p(\d+)', r2[0]).group(1)):
                return r1
            
            print(f'r1={r1[0]}, r2={r2[0]}, 还是要求助r1大模型！！！')
            r3 = await postmodel(prompt, 'deepseek_r1')
            print(f'r3={r3[0]}')
            return r3
        

        async def _process_item(item):
            """处理单个item的辅助函数"""
            res, total_tokens = await _model(item['prompt'])
            return res, total_tokens


        results = await asyncio.gather(
            *[_process_item(item) for item in groups],
            return_exceptions=True
        )

        for item, (res, total_tokens) in zip(groups, results):
            if isinstance(res, Exception):
                print(f"❌ 处理失败: {str(res)}")
                continue

            # #p9#
            item['model_output'] = res
            item['total_tokens'] = total_tokens
            if re.search(r'p\d+', res): item['target_line'] = int(re.search(r'p(\d+)', res).group(1))

        return groups


    def _handle_mark_material(groups, soup):
        """标记材料范围并清理错误标签
        
        参数:
            groups: list - 材料分组数据
            soup: BeautifulSoup - 解析后的HTML对象
            
        处理逻辑:
            1. 在材料起始和结束位置插入分割线
            2. 递归清理特定类型的data-label属性
            3. 跳过题号标签的处理
        """

        def _clear_specific_labels(node):
            """
            递归清理HTML节点中的特定data-label属性
            
            功能：
            1. 清理不需要的标签属性(header/answer/explanation/extra)
            2. 清理大于1级的题号标签(data-level>1的quest_num)
            
            参数:
                node: BeautifulSoup节点对象
                
            处理逻辑:
            - 递归遍历节点及其子节点
            - 检查每个节点的data-label属性
            - 根据规则删除特定标签属性
            """
            if hasattr(node, 'attrs') and 'data-label' in node.attrs:
                label = node.attrs['data-label']
                # 清理不需要的标签类型
                if label in ['header', 'answer', 'explanation', 'extra']:
                    del node.attrs['data-label']

                # 清理大于1级的题号标签
                if label == 'quest_num' and 'data-level' in node.attrs:
                    level = int(node.attrs['data-level'])
                    if level > 1: del node.attrs['data-label']
            
            # 递归处理子节点
            if hasattr(node, 'children'):
                for child in node.children:
                    if hasattr(child, 'attrs'):
                        _clear_specific_labels(child)


        for group in groups:
            if not 'target_line' in group:
                continue
            if group['target_line'] == len(group['groups']):
                continue
            if group['groups'][group['target_line'] - 1]['label'] == 'quest_num':
                continue

            # 若target_node是header，则向下传递到不是header为止
            i = group['target_line'] - 1
            while i < len(group['groups']) and \
                  re.search(r'data-label="header"', str(group['groups'][i]['node'])):
                i += 1
            if i >= len(group['groups']):
                group['target_line'] - 1
            if i == len(group['groups']) - 1:
                continue
            target_node = group['groups'][i]['node']
            
            hr_start = soup.new_tag('hr', attrs={'data-label': 'material_start_separator'})
            hr_end = soup.new_tag('hr', attrs={'data-label': 'material_end_separator'})
            # 前面插
            target_node.insert_before(hr_start)
            # 后面插
            group['groups'][-1]['node'].insert_before(hr_end)
            # 把中间的错误的data-label清掉
            for item in group['groups'][i:-1]:
                _clear_specific_labels(item['node'])

        return groups


    [groups, soup] = _handle_groups(html_content)
    groups = _handle_token(groups)
    groups = _get_prompt(groups)
    groups = await _post_model(groups)
    groups = _handle_mark_material(groups, soup)

    total_tokens = sum(item.get('total_tokens', 0) for item in groups)
    return str(soup), total_tokens


# print("开始备份machine.html文件...")
# backup_machine_files()
# print("备份操作完成")

asyncio.run(run())

# 2025.05.28处理到任务geography_19742433
# asyncio.run(run2())