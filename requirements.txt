aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
annotated-types==0.7.0
anyio==4.5.2
async-timeout==5.0.1
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
crcmod==1.7
cryptography==43.0.3
dnspython==2.6.1
exceptiongroup==1.2.2
faiss-cpu==1.8.0.post1
filelock==3.16.1
fsspec==2025.3.0
h11==0.14.0
httpcore==1.0.7
httpx==0.28.1
huggingface-hub==0.29.3
idna==3.10
Jinja2==3.1.6
jmespath==0.10.0
joblib==1.4.2
MarkupSafe==2.1.5
mpmath==1.3.0
networkx==3.1
numpy==1.24.4
# nvidia-cublas-cu12==12.1.3.1
# nvidia-cuda-cupti-cu12==12.1.105
# nvidia-cuda-nvrtc-cu12==12.1.105
# nvidia-cuda-runtime-cu12==12.1.105
# nvidia-cudnn-cu12==********
# nvidia-cufft-cu12==*********
# nvidia-curand-cu12==**********
# nvidia-cusolver-cu12==**********
# nvidia-cusparse-cu12==**********
# nvidia-nccl-cu12==2.20.5
# nvidia-nvjitlink-cu12==12.8.93
# nvidia-nvtx-cu12==12.1.105
oss2==2.19.1
packaging==24.2
pillow==10.4.0
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.10.6
pydantic_core==2.27.2
pymongo==3.11.4
python-dateutil==2.9.0.post0
PyYAML==6.0.2
redis==5.2.1
regex==2024.11.6
requests==2.32.3
safetensors==0.5.3
scikit-learn==1.3.2
scipy==1.10.1
sentence-transformers==3.2.1
six==1.17.0
sniffio==1.3.1
sympy==1.13.3
threadpoolctl==3.5.0
tokenizers==0.20.3
torch==2.4.1
tornado==6.4.2
tqdm==4.67.1
transformers==4.46.3
# triton==3.0.0
typing_extensions==4.12.2
ujson==5.10.0
urllib3==1.26.16
volcengine-python-sdk==1.0.128
