import tornado
from tornado.ioloop import <PERSON><PERSON><PERSON>
from tornado.web import <PERSON><PERSON><PERSON><PERSON><PERSON>, Application
from tornado.httpserver import HTTPServer
from tornado.options import options, define

import constants
from app.open_api.router import open
from constants import DEBUG, PORT
from app.basic.log import logger

if DEBUG:
    import tornado.autoreload


# 指定 port 启动，注释掉
# define('port', default=PORT, help='run on this port', type=int)
# tornado.options.parse_command_line()


class MainHandler(RequestHandler):
    def get(self):
        self.write('This message comes from Tornado ^_^')


def make_app() -> tornado.web.Application:
    urls = [(r'/tornado', MainHandler)]
    urls.extend(open.patterns)
    return Application(
        handlers=urls,
        debug=DEBUG
    )


def main():
    app = make_app()
    server = tornado.httpserver.HTTPServer(app, xheaders=True)
    server.listen(options.port)
    logger.info(f'server start at http://127.0.0.1:{options.port}')
    logger.info(f"{constants.ACCESS_KEY_ID} -- {constants.ACCESS_KEY_SECRET}")
    IOLoop.current().start()


if __name__ == "__main__":
    # main()

    import argparse
    parser = argparse.ArgumentParser(description='Description of your program')
    parser.add_argument('-i', '--instance_id', help='api service instance index', required=True)
    args = vars(parser.parse_args())

    app = make_app()
    server = tornado.httpserver.HTTPServer(app, xheaders=True)
    input_port = args['instance_id']
    server.listen(input_port)
    logger.info(f'server start at http://127.0.0.1:{input_port}')
    logger.info(f"{constants.ACCESS_KEY_ID} *** {constants.ACCESS_KEY_SECRET}")
    IOLoop.current().start()
