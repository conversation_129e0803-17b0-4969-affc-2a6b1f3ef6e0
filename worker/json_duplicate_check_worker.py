# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/25 18:37
import json

import requests

from app.enums.task import TaskType
from app.json_duplicate_check.node_check import NodeDuplicate<PERSON>heck
from app.basic.storagehandler import storage
from app.basic.util import utils
from app.basic.log import logger
from app.basic.api.mq import MQ


class JsonDuplicateCheckWorker:

    def __init__(self, task_id, json_url, bucket_name, upload_path):
        self.task_id = task_id
        self.json_url = json_url
        self.bucket_name = bucket_name
        self.upload_path = upload_path

        self.json_data = requests.get(self.json_url).json()

        self.task_type = TaskType.JSON_DUPLICATE_CHECK.value
        self.callback_data = {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'task_status': False,  # True 成功 False 失败
            'callback_info': {
                'task_id': self.task_id,
                'status': -1,  # 0 成功  1 失败
                'info': '',
                'result': '',
            }
        }

    def main(self):
        try:
            self.json_data = NodeDuplicateCheck(self.json_data, self.task_id).main()
            self.json_data = json.dumps(self.json_data)
            _, object_name = storage.put_object_from_string(self.bucket_name, self.upload_path, self.json_data)
            self.callback_data['task_status'] = True
            self.callback_data['callback_info']['status'] = 0
            self.callback_data['callback_info']['result'] = object_name
        except Exception as e:
            error_info = f'{self.task_id} error, {utils.format_error()}'
            logger.info(error_info)
            self.callback_data['callback_info']['status'] = 1
            self.callback_data['callback_info']['info'] = error_info

        MQ.callback_task(self.callback_data)
