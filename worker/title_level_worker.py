# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/3/12 10:49
import os
import re
import time

import requests

from app.basic.api.mq import MQ
from app.enums.task import TaskType
from app.basic.storagehandler import storage
from constants import BASE_DIR
from app.enums.subject import Subject
from app.enums.app_key import AppKey
from app.title_level.recall.pre_html import PreHtml
from app.title_level.recall.get_title import GetTitle
from app.title_level.recall.filter_case import FilterByCase
from app.title_level.recall.model_check import ModelCheck
from app.title_level.title_level import TitleSort
from app.title_level.recall.title_fuzzy_check import TitleF<PERSON><PERSON><PERSON>heck
from app.basic.util import html_util
from app.basic.log import logger
from app.basic.util import utils


class TitleLevelWorker:

    def __init__(
            self, task_id: str,
            html_url: str,
            subject: str,
            bucket_name: str,
            upload_path: str,
            is_ai_edit: bool,
            is_test: bool = False
    ):
        self.task_id = task_id
        self.html_url = html_url
        self.subject = subject
        self.bucket_name = bucket_name
        self.upload_path = upload_path
        self.is_ai_edit = is_ai_edit
        self.is_test = is_test

        self.html_url = self.html_url.replace('-internal.aliyuncs.com', '.aliyuncs.com')
        self.html_data = requests.get(self.html_url).content.decode('utf-8')
        # 初始化 HTML 格式
        self.html_list = html_util.split_html_v2(self.html_data, is_add_line=False)

        self.task_type = TaskType.TITLE_LEVEL.value
        self.stat = {}
        self.callback_data = {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'task_status': False,  # True 成功 False 失败
            'callback_info': {
                'task_id': self.task_id,
                'status': -1,  # 0 成功  1 失败
                'info': '',
                'result': '',
                'stat': self.stat,
            },
        }

    def main(self):
        try:
            start_time = time.time()
            logger.info(f"{self.task_id} TitleLevelWorker start.")
            if self.is_in_white_list():
                logger.info(f"{self.task_id} TitleLevelWorker 在白名单内，清洗将被执行")
                self.task()
            else:
                logger.info(f"{self.task_id} TitleLevelWorker 不在白名单内，直接返回 html data")

            _, object_name = storage.put_object_from_string(self.bucket_name, self.upload_path, self.html_data)
            self.callback_data['task_status'] = True
            self.callback_data['callback_info']['status'] = 0
            self.callback_data['callback_info']['result'] = object_name
            end_time = time.time()
            self.stat_merge(int(end_time - start_time))
            logger.info(f"{self.task_id} TitleLevelWorker success.")
        except Exception as e:
            error_info = f'{self.task_id} TitleLevelWorker error, {utils.format_error()}'
            logger.info(error_info)
            self.callback_data['callback_info']['status'] = 1
            self.callback_data['callback_info']['info'] = error_info

        if not self.is_test:
            MQ.callback_task(self.callback_data)
        else:
            return self.callback_data['callback_info']

    def task(self):
        self.html_list = PreHtml(self.html_list).main()

        # recall
        title_list = GetTitle(self.html_list, self.subject, self.task_id).main()
        title_list = FilterByCase(title_list, self.task_id).main()

        # 先去掉 recall 中的标题模糊查询，
        title_list = TitleFuzzyCheck(title_list, self.task_id).main()
        title_list, stat = ModelCheck(title_list, self.task_id).main()
        self.stat.update({'title_check': stat})

        # 层级处理
        title_list, stat = TitleSort(self.task_id, title_list).main()
        self.stat.update({'title_level': stat})

        for item in title_list:
            line_num = int(item['line_num'])
            title_level = item['level']

            html_str = self.html_list[line_num]
            html_str = re.sub(r'<p[^>]*?>', '<p>', html_str)
            html_str = html_str.replace('<p', f'<p data-label="header" data-level="{title_level}"')
            self.html_list[line_num] = html_str

        self.html_data = html_util.join_html(self.html_list)

    def is_in_white_list(self):
        # if self.is_test:
        #     return True

        if self.is_ai_edit:
            if self.subject in  (Subject.english.name, ):
                return False
            else:
                return True
        else:
            if self.subject in  (Subject.english.name, ):
                return False
            else:
                return True



    def stat_merge(self, total_cost_time: float):
        total_token = 0
        for k, item in self.stat.items():
            cost_token = item['cost_token']
            total_token += cost_token
        self.stat.update({'total': {'cost_token': total_token, 'cost_time': total_cost_time}})
