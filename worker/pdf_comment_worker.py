# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/24 16:49
from app.basic.log import logger
from app.pdf_comment.add_comment import PdfComment
from app.basic.api.mq import MQ
from app.enums.task import TaskType
from app.basic.util import utils


class PdfCommentWorker:

    def __init__(self, task_id: str, ticket_id: str, comment_dict: dict):
        self.task_id = task_id
        self.ticket_id = ticket_id
        self.comment_dict = comment_dict
        self.task_type = TaskType.PDF_COMMENT.value

        self.callback_data = {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'task_status': False,  # True 成功 False 失败
            'callback_info': {
                'task_id': self.task_id,
                'status': -1,  # 0 成功  1 失败
                'info': '',
                'result': '',
            },
        }

    def main(self):
        try:
            result = PdfComment(self.ticket_id, self.comment_dict).main()
            self.callback_data['task_status'] = True
            self.callback_data['callback_info']['status'] = 0
            self.callback_data['callback_info']['result'] = result
        except Exception as e:
            error_info = f'{self.task_id} error, {utils.format_error()}'
            logger.info(error_info)
            self.callback_data['callback_info']['status'] = 1
            self.callback_data['callback_info']['info'] = error_info

        MQ.callback_task(self.callback_data)
