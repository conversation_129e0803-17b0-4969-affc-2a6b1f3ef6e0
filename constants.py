import os
import socket
import requests

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_DIR = '/data/logs/ai_util'
LOG_BACKUP_COUNT = 7
LOG_MAX_BYTES = 1024 * 1024 * 1024 * 1024

HOSTNAME = socket.gethostname()
PROJECT_NAME = 'ai_util'

# UC APP KEY
UC_APP_KEY = 'bbbaae4559864879a3b3bb8a'
UC_APP_SECRET = 'dcfb6e699d7541d7a205d16703390737abcfa16c'

PORT = 8088

if HOSTNAME.startswith('ali') or os.environ.get('ENV') == 'product':
    ENV = 'stable'
    DEBUG = False

    # DB
    MONGO_URI = "mongodb://root:<EMAIL>:3717," \
                "dds-uf6fcc4e461ee5a43.mongodb.rds.aliyuncs.com:3717/admin?replicaSet=mgset-7558683"

    REDIS_URL = 'r-uf6c1chzshionnoflp.redis.rds.aliyuncs.com'
    REDIS_PORT = 6379
    REDIS_DB = 6
    REDIS_PASSWORD = ''
    BROKER_URL = f'redis://{REDIS_URL}/{REDIS_DB}'
    CRONTAB_BROKER_URL = f'redis://{REDIS_URL}/7'

    # 地址
    FILTER_TITLE_HOST = 'http://milvus_frps.hexinedu.com:7011'
    JSONPREPROCESS_HOST = 'http://jsonpreprocess.hexinedu.com'
    PUBLISH_HOST = 'http://publish.hexinedu.com'
    PUBLISH_OPEN_HOST = 'http://open.publish.hexinedu.com'
    PPT_HOST = 'http://sigma-ppt.hexinedu.com'
    WORD_HOST = 'http://sigma-word.hexinedu.com'
    RPC_HOST = 'tcp://************:200123'
    CACHE_LATEX_HOST = 'http://cache_latex.hexinedu.com'
    ROBOT_HOST = 'http://robot.hexinedu.com'
    USER_CENTER_HOST = 'http://uc.hexinedu.com'
    THEMIS_HOST = 'http://themis.hexinedu.com'
    XDOC_HOST = 'http://open.xdoc.hexinedu.com'
    # UC_HOST = 'http://uc.hexin.im'
    UC_HOST = 'http://uc.hexinedu.com'
    QUEUE_HOST = 'http://rbs-founder.hexinedu.com'
    FLUSH_HOST = 'http://flush.hexinedu.com'
    WORKSHEET_HOST = 'http://worksheet.hexinedu.com'
    WORKSHEET_OPEN_HOST = 'http://worksheet.hexinedu.com'
    OPEN_HOST = 'http://open.hexinedu.com'
    SMS_SERVER = "http://sms.ali.hexin.im:8888/sms"  # 短信服务地址

    # APP Secret
    THEMIS = {
        'themis63250c4628': '4dd2ef63e16e6589584dcb11b73598ed',  # 出版
        'themis10621dffd7': '5e90221708cf2d24230954809ccc59c0',  # XDOC
        'themis61a4d9c7c4': '41df062b6e090f41d216e71a527542be',  # business-publish
        'themis29f8f0980d': '444fd899e3e2f2d2a4d2f81144e24c86',  # 图库系统
    }
    WORKSHEET_APP_KEY = 'themis650e19718f'
    WORKSHEET_APP_SECRET = '7d49303f5cd07ade9cf2648c5bb6a434'

    # OSS
    WORKSHEET_DIR = 'worksheet'
    FILES_DIR = 'files'
    OSS_HOST = 'http://oss-cn-shanghai-internal.aliyuncs.com'
    OSS_URL = 'https://hexin-worksheet.oss-cn-shanghai-internal.aliyuncs.com'
    SIGMA_TEMP_OSS_URL = 'https://sigma-temp.oss-cn-shanghai-internal.aliyuncs.com'
else:
    ENV = 'dev'
    DEBUG = True  # 标记是否在开发环境

    # DB
    # MONGO_URI = 'mongodb://mongo.hexin.im'
    MONGO_URI = 'mongodb://root:<EMAIL>:3717/admin'  # &maxPoolSize=100
    REDIS_URL = 'r-uf6c1chzshionnoflppd.redis.rds.aliyuncs.com'
    REDIS_PORT = 6379
    REDIS_DB = 6
    REDIS_PASSWORD = 'sigmaLOVE2017'
    BROKER_URL = f'redis://:{REDIS_PASSWORD}@{REDIS_URL}:{REDIS_PORT}/{REDIS_DB}'
    CRONTAB_BROKER_URL = f'redis://:{REDIS_PASSWORD}@{REDIS_URL}:{REDIS_PORT}/7'

    # 地址
    # filter title 保持使用内网穿透地址，以防内网穿透失败导致本地可以上线就不行了这种情况
    FILTER_TITLE_HOST = 'http://milvus_frps.hexinedu.com:7011'  # 'http://************:19531'
    JSONPREPROCESS_HOST = 'http://jsonpreprocess.hexinedu.com'
    PUBLISH_HOST = 'http://publish.hexinedu.com'
    PUBLISH_OPEN_HOST = 'http://open.publish.hexinedu.com'
    PPT_HOST = 'http://sigma-ppt.hexinedu.com'
    WORD_HOST = 'http://sigma-word.hexinedu.com'
    RPC_HOST = 'tcp://************:200123'
    CACHE_LATEX_HOST = 'http://cache_latex.hexinedu.com'
    ROBOT_HOST = 'http://robot.hexinedu.com'
    USER_CENTER_HOST = 'http://uc.hexinedu.com'
    THEMIS_HOST = 'http://themis.hexinedu.com'
    XDOC_HOST = 'http://open.xdoc.hexinedu.com'
    # UC_HOST = 'http://uc.hexin.im'
    UC_HOST = 'http://uc.hexinedu.com'
    QUEUE_HOST = 'http://rbs-founder.hexinedu.com'
    FLUSH_HOST = 'http://flush.hexinedu.com'
    WORKSHEET_HOST = 'http://worksheet.hexinedu.com'
    WORKSHEET_OPEN_HOST = 'http://worksheet.hexinedu.com'
    OPEN_HOST = 'http://open.hexinedu.com'
    SMS_SERVER = "http://sms.ali.hexin.im:8888/sms"  # 短信服务地址

    THEMIS = {
        'themis63250c4628': '4dd2ef63e16e6589584dcb11b73598ed',  # 出版
        'themis10621dffd7': '5e90221708cf2d24230954809ccc59c0',  # XDOC
        'themis61a4d9c7c4': '41df062b6e090f41d216e71a527542be',  # business-publish
        'themis29f8f0980d': '444fd899e3e2f2d2a4d2f81144e24c86',  # 图库系统
    }
    WORKSHEET_APP_KEY = 'themis6ac63cb392'
    WORKSHEET_APP_SECRET = 'f22ab3125523658db2fcc54d7535658f'

    # OSS
    WORKSHEET_DIR = 'worksheet'
    FILES_DIR = 'files-dev'
    OSS_HOST = 'http://oss-cn-shanghai.aliyuncs.com'
    OSS_URL = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com'
    SIGMA_TEMP_OSS_URL = 'https://sigma-temp.oss-cn-shanghai.aliyuncs.com'


# 阿里云key, secret
import redis
redis_client = redis.Redis(host=REDIS_URL, port=REDIS_PORT, db=0, password=REDIS_PASSWORD, decode_responses=True)
FLUSH_APP_KEY = redis_client.hget('flush_key_secret', 'app_key')
FLUSH_APP_SECRET = redis_client.hget('flush_key_secret', 'app_secret')
key_secret = requests.get(
    url=f'{FLUSH_HOST}/api/aliyun/ak/get',
    params={'app_key': FLUSH_APP_KEY, 'app_secret': FLUSH_APP_SECRET}
).json()['data']
ACCESS_KEY_ID, ACCESS_KEY_SECRET = key_secret['access_key'], key_secret['access_secret']
print(ACCESS_KEY_ID, ACCESS_KEY_SECRET)

RAM_KEY_ID = 'LTAI5tSSz9z9XwSnctsUWmyh'
RAM_KEY_SECRET = '******************************'
RAM_ROLE_ARN = 'acs:ram::1952866374984923:role/ramosstest'
RAM_SESSION_NAME = 'RamOssTest'

# 公共 OSS 配置
WORKSHEET_BUCKET = 'hexin-worksheet'
SIGMA_TEMP_BUCKET = 'sigma-temp'
BOOK_STABLE_BUCKET = 'book-stable'
SIGMA_STABLE_DOC_BUCKET = 'sigma-stable-doc'
XDOC_BUCKET = 'xdoc-stable'
PUBLIC_OSS_URL_ORIGIN = 'https://hexin-worksheet.oss-cn-shanghai.aliyuncs.com'
PUBLIC_OSS_URL = f'http://{WORKSHEET_BUCKET}.hexinedu.com'
PUBLIC_OSS_URL_DL = 'https://dl.hexinedu.com'
PUBLIC_TEMP_HOST = 'https://temp.hexinedu.com'
SIGMA_TEMP_PUBLIC = 'https://sigma-temp.oss-cn-shanghai.aliyuncs.com'
OSS_PUBLIC_HOST_TEMPLATE = 'https://%s.oss-cn-shanghai.aliyuncs.com'


HEXIN_CUSTOMER_ID = ['2', '51', '102']

# 存放工单系统下载的数据的基础目录
DATA_BASE_PATH = '/Users/<USER>/export/'
ZIP_DIR = '/Users/<USER>/zip_dir'
if not os.path.exists(DATA_BASE_PATH):
    os.makedirs(DATA_BASE_PATH)
if not os.path.exists(ZIP_DIR):
    os.makedirs(ZIP_DIR)

# 大模型 AK
HUOSHAN_AK = 'c6b58699-3fd3-4497-9aee-ad18e920c24d'
