import fitz  # PyMuPDF
import os
import textwrap
from datetime import datetime
import io
from PIL import Image, ImageDraw, ImageFont
import re


class PDFCommentProcessor:
    """
    Final PDF comment processor with fixed positioning and better display:
    1. Converts PDF pages to images
    2. Creates a new PDF with these images and comments on both sides
    3. Adds high-quality visible comments in the margins with proper alignment
    """
    
    def __init__(self, 
                 input_pdf, 
                 output_pdf=None, 
                 margin_width=250,   # Reduced margin for more content space
                 font_size=10,       # Smaller font for compact layout
                 line_width=18,      # Width in characters (guidance)
                 dpi=100,            # Lower default DPI to reduce page image size
                 comment_padding=6,  # Tighter vertical packing
                 custom_font=None):
        """
        Initialize the PDF comment processor
        """
        self.input_pdf = input_pdf
        
        if not output_pdf:
            # Create output filename based on input filename
            base, ext = os.path.splitext(input_pdf)
            self.output_pdf = f"{base}_with_visible_comments{ext}"
        else:
            self.output_pdf = output_pdf
        
        # Style settings
        self.margin_width = margin_width
        self.font_size = font_size
        self.line_width = line_width
        self.dpi = dpi
        self.custom_font = custom_font
        self.comment_padding = comment_padding
        
        # High quality rendering settings
        self.background_color = (252, 252, 252)  # Almost white
        self.text_color = (0, 0, 0)  # Black
        self.border_color = (180, 180, 180)  # Light gray
        self.highlight_bg_color = (255, 255, 200)  # Light yellow for highlights
        
        # State variables
        self.annotations = []
        self.input_doc = None
        self.output_doc = None
        self.font = None
        self.english_font = None
    
    def setup_font(self):
        """
        Set up a font that properly supports Chinese characters
        """
        if self.custom_font and os.path.exists(self.custom_font):
            try:
                self.font = ImageFont.truetype(self.custom_font, self.font_size)
                print(f"Using custom font: {self.custom_font}")
                return
            except Exception as e:
                print(f"Could not load custom font: {e}")
                # Continue to try other fonts
        
        # Try to find a good font for Chinese characters.
        # If you want to use a different font, you can change the font_paths list.
        font_paths = [
            # WSL Windows fonts
            "/mnt/c/Windows/Fonts/msyh.ttc",  # Microsoft YaHei
            "/mnt/c/Windows/Fonts/simsun.ttc",  # SimSun
            "/mnt/c/Windows/Fonts/simhei.ttf",  # SimHei
            "/mnt/c/Windows/Fonts/simkai.ttf",  # KaiTi
            "/mnt/c/Windows/Fonts/simfang.ttf",  # FangSong
            # Linux fonts - Ubuntu/Debian
            "/usr/share/fonts/truetype/arphic/ukai.ttc",  # AR PL UKai
            "/usr/share/fonts/truetype/arphic/uming.ttc",  # AR PL UMing
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/opentype/noto/NotoSansSC-Regular.otf",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
            # Common user-installed font locations
            os.path.expanduser("~/.fonts/wqy-microhei.ttc"),
            # Fallbacks
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
        ]
        
        for path in font_paths:
            if os.path.exists(path):
                try:
                    self.font = ImageFont.truetype(path, self.font_size)
                    print(f"Using font: {path}")
                    return
                except Exception as e:
                    print(f"Failed to load font {path}: {e}")
        
        # Last resort - use default font
        print("Using PIL default font (may not display Chinese characters correctly)")
        self.font = ImageFont.load_default()
        # Prepare English font fallback (Arial/Times)
        # If you want to use a different font, you can change the english_paths list.
        self.english_font = self.font
        english_paths = [
            "/mnt/c/Windows/Fonts/arial.ttf",
            "/mnt/c/Windows/Fonts/times.ttf",
            "/mnt/c/Windows/Fonts/times new roman.ttf"
        ]
        for epath in english_paths:
            if os.path.exists(epath):
                try:
                    self.english_font = ImageFont.truetype(epath, self.font_size)
                    print(f"Using English font: {epath}")
                    break
                except Exception:
                    pass
    
    def is_cjk_char(self, char):
        """Check if a character is a CJK character"""
        return (
            # CJK unified ideographs
            '\u4e00' <= char <= '\u9fff' or
            '\u3400' <= char <= '\u4dbf' or
            '\uf900' <= char <= '\ufaff' or
            # CJK symbols and punctuation
            '\u3000' <= char <= '\u303f' or
            # Fullwidth ASCII variants (fullwidth punctuation, parentheses, etc.)
            '\uff00' <= char <= '\uffef' or
            # CJK Extension B and beyond
            '\U00020000' <= char <= '\U0002fa1f'
        )
    
    def has_cjk(self, text):
        """Check if text contains CJK characters"""
        return any(self.is_cjk_char(char) for char in text)
    
    def count_effective_chars(self, text):
        """Count characters giving more weight to CJK characters"""
        count = 0
        for char in text:
            if self.is_cjk_char(char):
                count += 2  # CJK characters count as 2
            else:
                count += 1
        return count
    
    def calculate_box_dimensions(self, text):
        """
        Calculate appropriate dimensions for a comment box based on text length
        Returns line_width, box_width
        """
        effective_char_count = self.count_effective_chars(text)
        
        # For very short comments
        if effective_char_count < 30:
            line_width = 15
            box_width = self.margin_width - 60
        # For medium length comments
        elif effective_char_count < 80:
            line_width = 18
            box_width = self.margin_width - 50
        # For longer comments
        else:
            # Scale up for longer comments
            line_width = min(25, 18 + effective_char_count // 40)  # Cap at 25
            box_width = min(self.margin_width - 30, self.margin_width - 50 + (effective_char_count // 20) * 10)
        
        # Adjust for pure CJK text
        if self.has_cjk(text) and effective_char_count / len(text) > 1.5:
            line_width = max(10, line_width - 5)  # Reduce line width for CJK-heavy text
        
        return line_width, box_width
    
    def extract_annotations(self):
        """
        Extract all annotations from the PDF file
        """
        self.input_doc = fitz.open(self.input_pdf)
        self.annotations = []
        
        for page_num, page in enumerate(self.input_doc):
            # Get all annotations on the page
            page_annots = []
            
            for annot in page.annots():
                # Get annotation content and metadata
                content = annot.info.get("content", "").strip()
                print(content)
                if not content:  # Skip annotations without content
                    continue
                    
                rect = annot.rect  # The annotation rectangle
                annot_type = annot.type[1]  # Annotation type
                
                # Get highlighted text for highlights
                highlight_text = ""
                if annot_type in ["Highlight", "Underline", "StrikeOut"]:
                    # Method 1: Try to use get_text with clip parameter
                    try:
                        highlight_text = page.get_text("text", clip=rect).strip()
                    except:
                        # If that fails, try alternative methods
                        pass
                        
                    # Method 2: If first method failed or returned empty string
                    if not highlight_text:
                        try:
                            words = page.get_text_words()
                            word_list = []
                            for word in words:
                                word_rect = fitz.Rect(word[:4])
                                if rect.intersects(word_rect):
                                    word_list.append(word[4])
                            highlight_text = " ".join(word_list)
                        except:
                            pass
                
                # Store the annotation data
                page_annots.append({
                    "page_num": page_num,
                    "content": content,
                    "rect": rect,
                    "type": annot_type,
                    "highlight_text": highlight_text,
                    "x_center": (rect.x0 + rect.x1) / 2  # Store center X for side determination
                })
            
            # Sort annotations on the page by y-coordinate (top to bottom)
            page_annots.sort(key=lambda a: a["rect"].y0)
            self.annotations.extend(page_annots)
        
        print(f"Found {len(self.annotations)} annotations")
        return self.annotations
    
    def wrap_cjk_text(self, text, width):
        """
        Specialized wrapping for CJK text - simpler and more effective approach
        """
        # Simply break after each "width" characters, without trying to be clever
        # This is actually more in line with CJK typographic traditions
        lines = []
        current_line = ""
        char_count = 0
        
        for char in text:
            # Count CJK characters as 2, everything else as 1
            current_char_width = 2 if self.is_cjk_char(char) else 1
            if char_count + current_char_width > width * 2:  # Using width*2 since we count CJK as 2
                lines.append(current_line)
                current_line = char
                char_count = current_char_width
            else:
                current_line += char
                char_count += current_char_width
        
        if current_line:
            lines.append(current_line)
        
        return lines
    
    def wrap_text(self, text, width):
        """
        Wrap text with specialized CJK handling
        """
        if self.has_cjk(text):
            return self.wrap_cjk_text(text, width)
        else:
            # For non-CJK text, use regular word wrapping
            return textwrap.wrap(text, width=width*2)  # Double width for non-CJK
    
    def render_comment_box(self, comment_text, max_box_width, highlight_text="", annot_type=""):
        """
        Render a high-quality comment box with text
        """
        # Only use the comment text itself
        display_text = comment_text
        # Prepare measurement context
        temp_img = Image.new('RGB', (1, 1), self.background_color)
        draw_temp = ImageDraw.Draw(temp_img)
        # Compute padding and max text width
        padding = self.comment_padding
        max_text_width = max_box_width - 2 * padding
        # Wrap text into lines respecting existing line breaks and max width
        lines = []
        for para in display_text.splitlines():
            if not para:
                lines.append("")
                continue
            current = ''
            # include whitespace tokens
            tokens = re.split(r'(\s+)', para)
            for token in tokens:
                if token == '':
                    continue
                test_line = current + token
                # measure width of test_line via textbbox
                bbox = draw_temp.textbbox((0, 0), test_line, font=self.font)
                w = bbox[2] - bbox[0]
                if w <= max_text_width:
                    current = test_line
                else:
                    if current:
                        lines.append(current)
                    # handle overflow token
                    # measure width of token
                    bbox = draw_temp.textbbox((0, 0), token, font=self.font)
                    tw = bbox[2] - bbox[0]
                    if tw <= max_text_width:
                        current = token.lstrip()
                    else:
                        # break token by character
                        tmp = ''
                        for ch in token:
                            # measure width of tmp+ch
                            bbox = draw_temp.textbbox((0, 0), tmp + ch, font=self.font)
                            cw = bbox[2] - bbox[0]
                            if cw <= max_text_width:
                                tmp += ch
                            else:
                                if tmp:
                                    lines.append(tmp)
                                tmp = ch
                        current = tmp
            if current:
                lines.append(current)
        # compute pixel widths of each line
        widths = []
        for line in lines:
            bbox = draw_temp.textbbox((0, 0), line, font=self.font)
            widths.append(bbox[2] - bbox[0])
        content_width = max(widths) if widths else 0
        # determine line height via test string
        bbox = draw_temp.textbbox((0, 0), 'Hg', font=self.font)
        fh = bbox[3] - bbox[1]
        line_spacing = int(fh * 1.2)
        # Final box dimensions
        img_width = min(max_box_width, content_width + 2 * padding)
        img_height = len(lines) * line_spacing + 2 * padding
        # Create the image
        img = Image.new('RGB', (img_width, img_height), self.background_color)
        draw = ImageDraw.Draw(img)
        # Draw border
        draw.rectangle([(1, 1), (img_width - 2, img_height - 2)], outline=self.border_color, width=1)
        # Draw each line, splitting Latin vs CJK runs
        for idx, line in enumerate(lines):
            y = padding + idx * line_spacing
            x = padding
            run = ''
            last_is_cjk = self.is_cjk_char(line[0]) if line else False
            for ch in line:
                is_cjk = self.is_cjk_char(ch)
                if is_cjk == last_is_cjk:
                    run += ch
                else:
                    # render accumulated run
                    font_to_use = self.font if last_is_cjk else self.english_font
                    draw.text((x, y), run, font=font_to_use, fill=self.text_color)
                    bbox = draw.textbbox((x, y), run, font=font_to_use)
                    x = bbox[2]
                    run = ch
                    last_is_cjk = is_cjk
            # draw final run
            if run:
                font_to_use = self.font if last_is_cjk else self.english_font
                draw.text((x, y), run, font=font_to_use, fill=self.text_color)
        # Return PIL image and its dimensions; compression is done in create_new_pdf
        return img, img_height, img_width
    
    def create_new_pdf_with_comments(self):
        """
        Create a new PDF with page images and comments in margins on both sides
        """
        if not self.annotations:
            print("No annotations found. No output file created.")
            return None
        
        # Set up font for text rendering
        self.setup_font()
        
        # Create a new PDF
        self.output_doc = fitz.open()
        
        # Process each page
        pages_with_annotations = set(annot["page_num"] for annot in self.annotations)
        
        # Keep track of used space on each page to avoid overlapping comments
        # Now we need to track left and right sides separately
        page_used_areas_left = {}
        page_used_areas_right = {}
        
        for page_num in range(len(self.input_doc)):
            # Get original page dimensions
            orig_page = self.input_doc[page_num]
            width = orig_page.rect.width
            height = orig_page.rect.height
            
            # Create a wider page to accommodate comments in margins on both sides
            new_width = width + self.margin_width * 2
            
            # Create a new page in the output PDF with the new width
            new_page = self.output_doc.new_page(width=new_width, height=height)
            
            # Render the original page at reduced DPI for smaller file size
            zoom_factor = self.dpi / 72
            pix = orig_page.get_pixmap(matrix=fitz.Matrix(zoom_factor, zoom_factor), alpha=False)
            # Compress page image to JPEG (default quality)
            page_bytes = pix.tobytes('jpeg')
            # Insert compressed page image
            new_page.insert_image(
                fitz.Rect(self.margin_width, 0, self.margin_width + width, height),
                stream=page_bytes
            )
            
            # If this page has annotations, add them to the margins
            if page_num in pages_with_annotations:
                # Initialize used areas for this page
                page_used_areas_left[page_num] = []
                page_used_areas_right[page_num] = []
                
                # Get annotations for this page
                page_annotations = [a for a in self.annotations if a["page_num"] == page_num]
                
                # Sort annotations by vertical position to maintain reading flow
                page_annotations.sort(key=lambda a: a["rect"].y0)
                
                # FORCE balanced distribution - alternate sides
                for i, annotation in enumerate(page_annotations):
                    content = annotation["content"]
                    rect = annotation["rect"]
                    annot_type = annotation["type"]
                    highlight_text = annotation.get("highlight_text", "")
                    
                    # Get original position of annotation
                    x0, y0 = rect.x0, rect.y0
                    
                    # Alternate sides regardless of annotation position
                    # Even indices go to left, odd to right
                    place_on_left = (i % 2 == 0)
                    
                    # Set margin position based on side
                    if place_on_left:
                        # Left margin position - ensure it doesn't overlap content
                        margin_x = 15  # Far left in left margin
                        page_used_areas = page_used_areas_left
                        connect_start = self.margin_width  # Connect from left edge of document
                        max_box_width = self.margin_width - 40  # Maximum width for left side
                    else:
                        # Right margin position
                        margin_x = self.margin_width + width + 15  # Left align in right margin
                        page_used_areas = page_used_areas_right
                        connect_start = self.margin_width + width  # Connect from right edge of document
                        max_box_width = self.margin_width - 40  # Maximum width for right side
                    
                    # Render the comment box with dynamic sizing
                    comment_img, comment_height, box_width = self.render_comment_box(
                        content,
                        max_box_width,
                        highlight_text,
                        annot_type
                    )
                    # Center box vertically around the annotation if possible
                    default_y = int(y0 - comment_height / 2)
                    margin_y = max(self.comment_padding,
                                   min(default_y,
                                       height - comment_height - self.comment_padding))
                    # Create a text box for the comment, using dynamic box_width
                    text_rect = fitz.Rect(
                        margin_x,
                        margin_y,
                        margin_x + box_width,
                        margin_y + comment_height
                    )
                    
                    # Check for overlaps with existing comments on the same side
                    overlaps = True
                    attempt_count = 0
                    max_attempts = 5
                    
                    while overlaps and attempt_count < max_attempts:
                        overlaps = False
                        attempt_count += 1
                        
                        if page_num in page_used_areas:
                            for used_rect in page_used_areas[page_num]:
                                if text_rect.intersects(used_rect):
                                    # Move down by the existing comment height plus small gap
                                    margin_y = used_rect.y1 + self.comment_padding//2
                                    
                                    if place_on_left:
                                        text_rect = fitz.Rect(
                                            margin_x, 
                                            margin_y, 
                                            margin_x + box_width,
                                            margin_y + comment_height
                                        )
                                    else:
                                        text_rect = fitz.Rect(
                                            margin_x, 
                                            margin_y, 
                                            margin_x + box_width,
                                            margin_y + comment_height
                                        )
                                    
                                    overlaps = True
                                    break
                        
                        # Avoid comments going off the bottom of the page
                        if margin_y + comment_height > height - self.comment_padding:
                            if attempt_count == max_attempts:
                                # If too low, reset to top margin
                                margin_y = self.comment_padding
                            if place_on_left:
                                # Try placing at the top
                                margin_y = self.comment_padding
                            if place_on_left:
                                # Try placing at the top
                                margin_y = self.comment_padding
                            
                            # Check if placing at top causes overlap
                            top_overlaps = False
                            if page_num in page_used_areas:
                                for used_rect in page_used_areas[page_num]:
                                    if text_rect.intersects(used_rect):
                                        top_overlaps = True
                                        break
                            
                            if not top_overlaps:
                                overlaps = False
                            else:
                                # Try again after last comment
                                if page_num in page_used_areas and page_used_areas[page_num]:
                                    last_rect = max(page_used_areas[page_num], key=lambda r: r.y1)
                                    margin_y = last_rect.y1 + self.comment_padding//2
                                    if place_on_left:
                                        text_rect = fitz.Rect(
                                            margin_x, 
                                            margin_y, 
                                            margin_x + box_width,
                                            margin_y + comment_height
                                        )
                                    else:
                                        text_rect = fitz.Rect(
                                            margin_x, 
                                            margin_y, 
                                            margin_x + box_width,
                                            margin_y + comment_height
                                        )
                    
                    # Add to used areas with padding
                    padded_rect = fitz.Rect(
                        text_rect.x0 - 5,
                        text_rect.y0 - 10,
                        text_rect.x1 + 5,
                        text_rect.y1 + 25  # More padding below
                    )
                    
                    if page_num not in page_used_areas:
                        page_used_areas[page_num] = []
                    
                    page_used_areas[page_num].append(padded_rect)
                    
                    # Add a visible line connecting the annotation to the comment
                    connect_y = margin_y + comment_height / 2
                    
                    # Adjust connector line based on side
                    if place_on_left:
                        # For left side, connect from right edge of comment to annotation
                        new_page.draw_line(
                            fitz.Point(text_rect.x1, connect_y),
                            fitz.Point(self.margin_width + x0, y0),
                            color=(0.5, 0.5, 0.5),
                            width=0.8
                        )
                    else:
                        # For right side, connect from left edge of comment to annotation
                        new_page.draw_line(
                            fitz.Point(text_rect.x0, connect_y),
                            fitz.Point(self.margin_width + x0, y0),
                            color=(0.5, 0.5, 0.5),
                            width=0.8
                        )
                    
                    # Draw a small indicator at the annotation point
                    new_page.draw_circle(
                        fitz.Point(self.margin_width + x0, y0), 
                        1.5, 
                        color=(0.8, 0, 0), 
                        fill=(0.8, 0, 0)
                    )
                    
                    # Convert PIL image to JPEG bytes (lower quality) to reduce size
                    img_bytes = io.BytesIO()
                    comment_img.save(img_bytes, format='JPEG', quality=70)
                    img_bytes.seek(0)
                    # Insert the image into the PDF
                    new_page.insert_image(text_rect, stream=img_bytes)
                    
                    # Draw comment number for better reference
                    comment_number = i + 1
                    new_page.insert_text(
                        fitz.Point(self.margin_width + x0 + 5, y0 - 3),
                        f"{comment_number}",
                        color=(0.8, 0, 0),
                        fontsize=8
                    )
        
        # Save the result
        try:
            self.output_doc.save(self.output_pdf)
            print(f"Created new PDF with visible comments: {self.output_pdf}")
            return self.output_pdf
        except Exception as e:
            print(f"Error saving new PDF: {str(e)}")
            return None
        finally:
            self.input_doc.close()
            self.output_doc.close()
    
    def process(self):
        """
        Process the PDF file: extract annotations and create a new PDF with comments
        """
        print(f"Processing {self.input_pdf}...")
        self.extract_annotations()
        return self.create_new_pdf_with_comments()


def main():
    """
    Main function to process command line arguments
    """
    processor = PDFCommentProcessor(
        input_pdf='./2024年全国统一高考英语试卷（甲卷）.pdf',
        output_pdf=None,
        margin_width=250,
        font_size=10,
        line_width=18,
        dpi=100,
        comment_padding=6,
        custom_font="/System/Library/Fonts/PingFang.ttc"
    )
    
    result = processor.process()
    
    if result:
        print(f"Successfully processed PDF. Output saved to: {result}")
    else:
        print("Failed to process PDF.")


if __name__ == "__main__":
    main()
