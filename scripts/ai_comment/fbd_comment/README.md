# PDF Commenter Tool

This tool extracts highlights and comments from a PDF file and generates a new PDF with visible, standalone comment boxes in the document margins. It fully supports Chinese (CJK) and mixed-language (English) content, dynamic box sizing, and compact left/right placement.

Author @ Slimshilin

Date @ 20250424

## Features

- Extracts PDF annotations (highlight, underline, strikeout) and their text
- Renders comment boxes on both left and right margins (alternating per comment)
- Full support for Chinese (CJK) characters and punctuation, plus English text
- Dynamic wrapping based on pixel measurements and existing line breaks
- Automatic line-break insertion for overflowing lines
- Dynamic box sizing: shrunken for short comments, expanded up to a configurable max width
- Smaller default font (10pt) and compact padding for high comment density
- JPEG compression for page images and comment boxes to minimize PDF size

## Requirements

- Python 3.8+
- Conda or virtual environment

## Dependencies

```bash
pip install PyMuPDF pillow
```

Or with Conda:

```bash
conda create -n PDF_Commenter python=3.9
conda activate PDF_Commenter
pip install PyMuPDF pillow
```

## Font Setup

- Chinese font paths are auto-detected (e.g., `msyh.ttc`, `simhei.ttf`).
- English fallback fonts: Arial or Times New Roman (e.g. `C:\Windows\Fonts\arial.ttf`).
- To override, pass `--font /path/to/font.ttf`.

## Usage

```bash
conda activate PDF_Commenter
python display_pdf_with_comments.py \
  "./2024年全国统一高考英语试卷（甲卷）.pdf" \
  --margin-width 250 \
  --font-size 10 \
  --dpi 100 \
  --padding 6 \
  --font "/System/Library/Fonts/PingFang.ttc"
```

- `input.pdf`: source PDF with annotations
- `--margin-width`: width (points) of each side margin
- `--font-size`: comment font size (pt)
- `--dpi`: rendering DPI for page images, use this to control the output PDF size
- `--padding`: internal padding (px) in comment boxes
- `--font`: custom font supporting CJK

The output PDF will be saved as `{input}_with_visible_comments.pdf` by default.

## Example

```bash
python display_pdf_with_comments.py \
  "2024年全国统一高考英语试卷（甲卷）.pdf" \
  --margin-width 250 \
  --font-size 10 \
  --dpi 100 \
  --padding 6 \
  --font "/mnt/c/Windows/Fonts/msyh.ttc"
```

## Development Notes

- The script uses pixel-based wrapping via `PIL.ImageDraw.textbbox` to handle mixed CJK/Latin accurately.
- CJK punctuation is recognized in the Unicode fullwidth block to prevent orphaned marks.
- Comment boxes alternate left/right for maximum density.
