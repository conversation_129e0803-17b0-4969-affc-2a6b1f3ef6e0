import re
from bs4 import BeautifulSoup


def del_html_entities(s):
    html_entities = ["&nbsp;", "&lt;", "&gt;", "&amp;", "&quot;", "&apos;"]
    for e in html_entities:
        s = s.replace(e, "")
    return s


def del_html_tag(s):
    res = re.sub(r'<[^<>]*?>', '', s)
    return res


def split_html(s) -> list:
    res = re.split(r'\n', s)
    return res


def split_html_v2(html_content, is_add_line=False):
    soup = BeautifulSoup(html_content, 'html.parser')
    elements = []
    index = 0
    for tag in soup.find_all(['p', 'table']):
        # 检查当前标签是否是 table 的子标签
        if tag.find_parent('table'):
            continue
        data = str(tag)
        # 添加 line 标签
        if is_add_line:
            pattern = re.compile(r'\s*(<p)|(<table)')
            if pattern.search(data):
                data = pattern.sub(lambda x: x.group() + f' line="{index}"', data, 1)
        elements.append(data)
        index += 1
    return elements


def join_html(html_list, is_del_line=True):
    data = '\n'.join(html_list)
    # 去掉 line 标签
    if is_del_line:
        data = re.sub(r'\s*line="\d+"', '', data)
    return data


def add_check_span(s):
    # if '###' in s:
    #     return s
    # return f'###{s}|||'

    if 'data-check="ai_check"' in s:
        return s
    return f'<span data-check="ai_check">{s}</span>'


def add_discard_span(s):
    if s:
        res = f'<span data-label="discard">{s}</span>'
    else:
        res = ''
    return res


def add_quest_num_span(s, level):
    return f'<span data-label="quest_num" data-level="{level}">{s}</span>'


def get_prefix_text(s):
    s = re.sub(r'<[^<>]*?>', '', s)
    s = re.sub(r'\$\$.*?\$\$', '', s)
    if len(s) > 10:
        return s[:10]
    else:
        return s


def get_serial_number(s, level=1):
    pattern = re.compile(rf'<span[^<>]*?data-label="quest_num" data-level="{level}"[^<>]*?>(.*?)</span>')
    if pattern.search(s):
        serial_number = pattern.search(s).group(1)
    else:
        serial_number = ''
    return serial_number
