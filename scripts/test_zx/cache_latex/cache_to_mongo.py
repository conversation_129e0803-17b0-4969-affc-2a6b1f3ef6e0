# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/21 19:29
import collections
import os
import re

from app.basic.db.mongo import mongo_cli
from app.basic.util import utils


def main(subject):
    _dir = f'./temp/latex_text/{subject}'
    name_list = os.listdir(_dir)
    data = []
    for name in name_list:
        path = os.path.join(_dir, name)
        with open(path, 'r', encoding='utf-8') as fp:
            s = fp.read()
            # if 'ATP' in s:
            #     print()
            tmp = s.splitlines()
            data += tmp
    data = list(set(data))
    len_data = len(data)
    print(len_data)
    cache_dict = collections.defaultdict(list)
    for index, latex_str in enumerate(data):
        print(f'{index} / {len_data}')
        re_result = re.search(r'(.*?)\$\$(.*?)\$\$(.*)', latex_str)
        pre_text, latex_text, post_text = re_result.group(1), re_result.group(2), re_result.group(3)
        # print(pre_text, post_text, latex_text, latex_str, sep=' *** ')
        pred_latex_text = latex_text
        pred_latex_text = clean_latex(pred_latex_text)
        pred_latex_md5 = utils.get_str_md5(pred_latex_text)
        # if pred_latex_md5 in cache_dict:
        #     print()
        cache_dict[pred_latex_md5].append({
            'latex': latex_text,
            'pre': pre_text,
            'post': post_text,
        })

    # 检查 md5 是否有重复
    for md5, items in cache_dict.items():
        compare_latex = items[0]['latex']
        compare_latex = clean_latex(compare_latex)
        for item in items:
            latex = item['latex']
            latex = clean_latex(latex)
            if compare_latex != latex:
                print()

    # 入库
    cache_len = len(cache_dict)
    for batch, start_index in utils.chunk(cache_dict.items(), 1000):
        print(f'{start_index} / {cache_len}')
        insert_data = []
        for b in batch:
            md5, item = b
            insert_data.append({
                'md5': md5,
                'list': item,
            })
        mongo_cli['ai-util'][f'{subject}_latex_cache_new'].insert_many(insert_data)


def clean_latex(latex):
    pattern = r'\\mathrm\{(.*?)\}'
    while re.search(pattern, latex):
        latex = re.sub(pattern, lambda match: match.group(1), latex)
    return latex


if __name__ == '__main__':
    main('math')
