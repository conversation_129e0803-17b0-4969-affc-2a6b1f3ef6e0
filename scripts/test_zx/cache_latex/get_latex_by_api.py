import collections
import os
import time

import requests
import re
import json
import multiprocessing
from tqdm import tqdm

from scripts import COOKIE
from app.basic.util import html_util
from app.basic.util import utils


def get_html_url(subject_list):
    html_url_dict = collections.defaultdict(list)
    for i in range(1, 10000):
        print(f"{i}/10000")
        response = requests.get(
            url='http://xdoc.open.hexinedu.com/api/admin/project/getListByAdmin',
            params={
                'status': 14,
                'isOutAdmin': False,
                'filterPriority7': False,
                'page': i,
                'pageSize': 100,
            },
            cookies=COOKIE
        )
        data = response.json()['data']['projects']
        if not data:
            break

        for item in data:
            resource_type = item.get('taskResourceType', -1)
            if resource_type != 5:
                continue
            html_url = item.get('htmlUrl', '')
            if not html_url:
                continue
            xdoc_subject = item.get('meta', {}).get('subject', '')
            if xdoc_subject not in subject_list:
                continue

            html_url_dict[xdoc_subject].append({
                'html_url': html_url,
                'project_id': str(item['id']),
            })

    for subject, html_list in html_url_dict.items():
        json.dump(html_list, open(f'./temp/html_url/{subject}.json', 'w', encoding='utf-8'), ensure_ascii=False)


def download_html():
    # 'math', 'physics', 'chemistry',
    subject_list = ['math', 'physics', 'chemistry']
    for subject in subject_list:
        html_url_list = json.load(open(f'./temp/html_url/{subject}.json', 'r', encoding='utf-8'))
        lst_len = len(html_url_list)
        for index, item in enumerate(html_url_list):
            print(f"{subject} * {index}/{lst_len}")
            html_url = item['html_url']
            project_id = item['project_id']
            html_data = requests.get(html_url).content.decode('utf-8')

            _dir = f'./temp/html_data/{subject}'
            if not os.path.exists(_dir):
                os.makedirs(_dir)

            with open(f'{_dir}/{project_id}.html', 'w', encoding='utf-8') as f:
                f.write(html_data)


def get_latex_text():
    # 'math', 'physics', 'chemistry', 'biology'
    subject_list = ['chemistry']
    for subject in subject_list:
        _dir = f'/mnt/e/data/ai-util/cache_latex/html_data/{subject}'
        name_list = os.listdir(_dir)
        batch_group_list = utils.chunk_v2(name_list, 200)
        new_batch_group_list = []
        for index, batch in enumerate(batch_group_list):
            tmp = {
                'subject': subject,
                'batch': batch,
                'index': index,
                'dir': _dir,
            }
            new_batch_group_list.append(tmp)
            # get_latex_helper(tmp)

        with multiprocessing.Pool(processes=multiprocessing.cpu_count()) as pool:
            results = list(tqdm(pool.imap(get_latex_helper, new_batch_group_list), total=len(new_batch_group_list)))


def get_latex_helper(data):
    subject, batch, batch_index, subject_dir = data['subject'], data['batch'], data['index'], data['dir']
    total_latex_list = []
    for index, name in enumerate(batch):
        path = os.path.join(subject_dir, name)
        with open(path, 'r', encoding='utf-8') as f:
            html_data = f.read()
        if 'InvalidObjectState' in html_data:
            continue

        start_time = time.time()
        html_list = html_util.split_html_v2(html_data)
        during_time_1 = utils.round(time.time() - start_time, 2)
        start_time = time.time()
        latex_list = get_by_html(html_list)
        during_time_2 = utils.round(time.time() - start_time, 2)
        latex_list = list(set(latex_list))
        total_latex_list += latex_list
        total_latex_list = list(set(total_latex_list))
        print(f'{name} {subject} * {index}/{len(batch)} * {during_time_1} * {during_time_2} * latex 长度：{len(latex_list)} * html 长度：{len(html_list)}')

    start_time = time.time()
    latex_dir = f'/mnt/e/data/ai-util/cache_latex/latex_text/{subject}'
    if not os.path.exists(latex_dir):
        os.makedirs(latex_dir)
    with open(os.path.join(latex_dir, f'latex_text_{batch_index}.txt'), 'w', encoding='utf-8') as fp:
        fp.write('\n'.join(total_latex_list))
    during_time = utils.round(time.time() - start_time, 2)
    print(f'{subject} *** 保存耗时 {during_time}')


def get_by_html(html_list):
    pre_length = 6
    post_length = 6
    latex_list = []
    for data in html_list:
        if '$$' not in data:
            continue

        data = html_util.del_html_tag(data)
        data = html_util.del_html_entities(data)

        def repl(m):
            nonlocal data
            latex_text = m.group()
            regs = m.regs[0]
            start_index = regs[0]
            end_index = regs[1]
            # latex 穿透
            start_time = time.time()
            pre_data = data[:start_index]
            pre_data = _clean_pre_post_data(pre_data, 'pre')
            post_data = data[end_index:]
            post_data = _clean_pre_post_data(post_data, 'post')
            during_time_1 = utils.round(time.time() - start_time, 2)
            start_time = time.time()
            valid_pre_data = pre_data[-pre_length:]
            valid_post_data = post_data[:post_length]
            during_time_2 = utils.round(time.time() - start_time, 2)
            if during_time_1 > 0.1 or during_time_2 > 0.1:
                print(f'>>>>> {during_time_1} *** {during_time_2}')
            result = valid_pre_data + latex_text + valid_post_data
            latex_list.append(result)
        re.sub(r'\$\$.*?\$\$', repl, data)
    return latex_list


def _clean_pre_post_data(data, tag):
    data = re.sub(r'\$\$.*?\$\$', '', data)
    # 待添加的 / \
    t = '.,;:?!，。、；：？！“”‘’（）【】《》■\s'
    if tag == 'pre':
        data = re.sub(rf'.*[{t}]', '', data)
    if tag == 'post':
        data = re.sub(rf'[{t}].*', '', data)
    data = data.strip()
    return data


if __name__ == '__main__':
    # math physics chemistry biology
    # get_html_url(['math', 'physics', 'chemistry', 'biology'])
    # download_html()
    get_latex_text()
