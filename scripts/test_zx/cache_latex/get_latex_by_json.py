# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/21 16:53
import os
import json
import re

import html_util


def main(_dir, subject):
    list_dir = os.listdir(_dir)
    len_file = len(list_dir)
    total_latex_list = []
    for index, name in enumerate(list_dir):
        if '.json' not in name:
            continue
        path = os.path.join(_dir, name)
        print(f"{index}/{len_file} *** {name}")
        json_data = json.load(open(path, 'r', encoding='utf-8'))
        html_list = json_to_html(json_data)
        latex_list = get_by_html(html_list)
        latex_list = list(set(latex_list))
        total_latex_list += latex_list

    total_latex_list = list(set(total_latex_list))
    with open(f'./{subject}_latex.txt', 'w', encoding='utf-8') as fp:
        fp.write('\n'.join(total_latex_list))


def json_to_html(json_data):
    def recursion(data):
        nonlocal html_str
        if isinstance(data, dict):
            return {key: recursion(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [recursion(_item) for _item in data]
        elif isinstance(data, (int, float)):
            return
        else:
            if '$$' not in data:
                return
            if '</p>' not in data:
                data = f'<p>{data}</p>'
            html_str += data

    html_str = ''
    for item in json_data:
        recursion(item)
    html_list = html_util.split_html_v2(html_str)
    return html_list


def get_by_html(html_list):
    # 输入数据格式归一化

    pre_length = 6
    post_length = 6
    latex_list = []
    for data in html_list:
        data = html_util.del_html_tag(data)
        data = html_util.del_html_entities(data)

        def repl(m):
            nonlocal data
            latex_text = m.group()
            regs = m.regs[0]
            start_index = regs[0]
            end_index = regs[1]
            # latex 穿透
            pre_data = data[:start_index]
            pre_data = _clean_pre_post_data(pre_data, 'pre')
            post_data = data[end_index:]
            post_data = _clean_pre_post_data(post_data, 'post')
            valid_pre_data = pre_data[-pre_length:]
            valid_pre_data = _clean_valid_pre_post_data(valid_pre_data)
            valid_post_data = post_data[:post_length]
            valid_post_data = _clean_valid_pre_post_data(valid_post_data)
            result = valid_pre_data + latex_text + valid_post_data
            latex_list.append(result)
        re.sub(r'\$\$.*?\$\$', repl, data)
    return latex_list


def _clean_pre_post_data(data, tag):
    t = '.,;:?!，。、；：？！“”‘’（）【】《》'
    if tag == 'pre':
        data = re.sub(rf'.*[{t}]', '', data)
    if tag == 'post':
        data = re.sub(rf'[{t}].*', '', data)
    data = re.sub(r'\$\$.*?\$\$', '', data)
    data = re.sub(r'，+', '，', data)
    data = re.sub(r'\s+', ' ', data)
    data = re.sub(r'：\s*，\s*', '：', data)
    data = re.sub(r'、+', '、', data)
    data = re.sub(r'[“”"]', '', data)
    return data


def _clean_valid_pre_post_data(data):
        data = re.sub(r'^[\d\-，.】）；、”A-Za-z]+', '', data)
        data = re.sub(r'[\d\-，.【；（A-Za-z]+$', '', data)
        data = re.sub(r'^.，', '', data)
        return data


if __name__ == '__main__':
    main('./temp', 'math')
