import json
import ast
import asyncio

from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.enums.prompt import GetPrompt


async def main(latex_list: list):
    latex_input = []
    for index, b in enumerate(latex_list):
        latex_input.append({
            'id': index + 1,
            'input': b
        })
    latex_input = json.dumps(latex_input, ensure_ascii=False)
    count = len(latex_list)
    prompt = GetPrompt.fix_latex_zx(latex_input, count)

    # 请求大模型
    db_res, ds_v3_res, db_r1_res = await asyncio.gather(
        Doubao.async_chat(prompt, DBModel.V15_PRO_32.value, 0.1),
        Doubao.async_chat(prompt, DBModel.DS_V3.value, 0.1),
        Doubao.async_chat(prompt, DBModel.DS_R1.value, 0.1),
    )
    print(db_res)
    print(ds_v3_res)
    print(db_r1_res)


def get_model_res_list(model_res):
    model_res = model_res.strip()
    model_res = model_res.replace('```JSON', '').replace('```', '')
    model_res_list = ast.literal_eval(model_res)
    return model_res_list


if __name__ == '__main__':
    asyncio.run(
        main(
            ["又“平衡点”$$(-2,5)$$在平移后的抛", "故答案为：$$(1,1)$$"]
        )
    )
