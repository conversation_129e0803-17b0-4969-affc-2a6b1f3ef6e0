import asyncio
import json
import traceback
import os
import re
import ast

from app.basic.util import latex_util, html_util, utils
from app.basic.api.doubao import <PERSON><PERSON><PERSON>, DBModel
from app.enums.prompt import GetPrompt
from app.basic.storagehandler import storage
from app.basic.baseerror import VerifyError

"""
用来走查 latex 的编辑器任务
http://xdoc.open.hexinedu.com/#/split/file/19026455

正斜体判断思路：
同时请求 doubao1.5 和 deepseek V3，将输出结果做对比，把不一样的内容收集，最后再用 deepseek R1 跑一遍
"""

async def main(valid_subject):
    _dir = f'./temp/{valid_subject}'
    fail_prompt_dir = f'./temp/fail_prompt'
    check_html_dir = f'./temp/{valid_subject}_check'
    if not os.path.exists(fail_prompt_dir):
        os.makedirs(fail_prompt_dir)
    if not os.path.exists(check_html_dir):
        os.makedirs(check_html_dir)

    file_name_list = os.listdir(_dir)
    for index, file_name in enumerate(file_name_list):
        print(file_name)
        path = os.path.join(_dir, file_name)
        if not path.endswith('.html'):
            continue

        latex_check_html = await fix_task_latex(file_name, _dir, fail_prompt_dir)

        check_name = file_name.replace('.machine', '.check')
        with open(os.path.join(check_html_dir, check_name), 'w', encoding='utf-8') as f:
            f.write(latex_check_html)

        storage.put_object_from_string(
            bucket_name='xdoc-stable',
            object_name='open/fc7539b21810cd4f0f0fb620/task/19026455.html',
            obj=latex_check_html,
        )


async def fix_task_latex(file_name, _dir, fail_prompt_dir):
    latex_check_html = ''
    latex_check_html += f'<p><b>{file_name}</b></p>\n'
    path = os.path.join(_dir, file_name)

    with open(path, 'r', encoding='utf-8') as f:
        html_data = f.read()
    latex_list_with_pre_post = get_by_html(html_data)
    # 去重
    latex_list_with_pre_post = list(set(latex_list_with_pre_post))

    # 收集失败的 input 最后统一用 deepseek R1 跑
    failed_input = []
    for batch, start_index in utils.chunk(latex_list_with_pre_post, 10):
        latex_input = []
        for batch_index, b in enumerate(batch):
            latex_input.append({
                'id': batch_index + 1,
                'input': b
            })
        latex_input = json.dumps(latex_input, ensure_ascii=False)
        prompt = GetPrompt.fix_latex_zx(latex_input, len(batch))
        model_res = ''

        try:
            # 整体来说，豆包喜欢自己新加一些无影响的定义，比如 $$-5$$ 会输出 $$\\text{-5}$$，deepseek V3 和 R1 都不会
            doubao_res_list, deepseek_v3_res_list = await asyncio.gather(
                request_doubao(prompt, len(batch)),
                request_deepseek_v3(prompt, len(batch))
            )
            for i, item in enumerate(doubao_res_list):
                # 对比两边返回把不一样的输出添加到 failed_input
                doubao_latex = item['fixed']
                deepseek_latex = deepseek_v3_res_list[i]['fixed']
                # 输出有一定概率带出上下文，只取 latex 对比
                doubao_latex = re.search(r'\$\$.*?\$\$', doubao_latex).group()
                deepseek_latex = re.search(r'\$\$.*?\$\$', deepseek_latex).group()
                # 因为重点是在处理正斜体，所以这里交叉验证只专注 mathrm 数量
                if doubao_latex.count('mathrm') != deepseek_latex.count('mathrm'):
                    failed_input.append(batch[i])
                else:
                    origin_latex_html = f'<p><b>刷前：</b>{batch[i]}</p>\n'
                    latex_html = f'<p><b>刷后：</b>{doubao_latex} *** {deepseek_latex}</p>\n'
                    latex_check_html += f'{origin_latex_html}{latex_html}'
        except Exception as e:
            # 如果请求模型失败，或者是模型输出结果校验失败，则全部的数据都记为失败
            failed_input += batch
            error_info = str(traceback.format_exc())
            print(error_info)
            txt_name = file_name.replace('.html', '')
            txt_name = f"{txt_name}_{start_index}_step1.txt"
            with open(os.path.join(fail_prompt_dir, txt_name), 'w', encoding='utf-8') as f:
                f.write(prompt + '\n\n\n\n' + model_res + '\n\n\n\n' + error_info)

    for batch, start_index in utils.chunk(failed_input, 10):
        latex_input = []
        for batch_index, b in enumerate(batch):
            latex_input.append({
                'id': batch_index + 1,
                'input': b
            })
        latex_input = json.dumps(latex_input, ensure_ascii=False)
        prompt = GetPrompt.fix_latex_zx(latex_input, len(batch))
        model_res = ''

        try:
            model_res_list = request_deepseek_r1(prompt, len(batch))
            for i, item in enumerate(model_res_list):
                fixed_latex = item['fixed']
                fixed_latex = re.search(r'\$\$.*?\$\$', fixed_latex).group()
                origin_latex_html = f'<p><b>刷前：</b>{batch[i]}</p>\n'
                latex_html = f'<p><b>R1刷后：</b>{fixed_latex}</p>\n'
                latex_check_html += f'{origin_latex_html}{latex_html}'
        except Exception as e:
            error_info = str(traceback.format_exc())
            print(error_info)
            txt_name = file_name.replace('.html', '')
            txt_name = f"{txt_name}_{start_index}_step2.txt"
            with open(os.path.join(fail_prompt_dir, txt_name), 'w', encoding='utf-8') as f:
                f.write(prompt + '\n\n\n\n' + model_res + '\n\n\n\n' + error_info)

    return latex_check_html


async def request_doubao(prompt, input_count):
    # 请求 doubao
    model_res = await Doubao.async_chat(prompt, DBModel.V15_PRO_32.value, 0.1)
    model_res = model_res.strip()
    model_res = model_res.replace('```JSON', '').replace('```', '')
    model_res_list = ast.literal_eval(model_res)
    check_model_res(model_res_list)
    if input_count != len(model_res_list):
        raise VerifyError("输出数量不对！")
    return model_res_list


async def request_deepseek_v3(prompt, input_count):
    model_res = await Doubao.async_chat(prompt, DBModel.DS_V3.value, 0.1)
    model_res = model_res.strip()
    model_res = model_res.replace('```JSON', '').replace('```', '')
    model_res_list = ast.literal_eval(model_res)
    check_model_res(model_res_list)
    if input_count != len(model_res_list):
        raise VerifyError("输出数量不对！")
    return model_res_list


def request_deepseek_r1(prompt, input_count):
    model_res = Doubao.chat(prompt, DBModel.DS_R1, 0.1)
    model_res = model_res.strip()
    model_res = model_res.replace('```JSON', '').replace('```', '')
    model_res_list = ast.literal_eval(model_res)
    check_model_res(model_res_list)
    if input_count != len(model_res_list):
        raise VerifyError("输出数量不对！")
    return model_res_list


def check_model_res(model_res_list):
    for r in model_res_list:
        if r.get('id') and r.get('input', '').count('$$') == 2 and r.get('fixed', '').count('$$') == 2:
            continue
        raise VerifyError(f"输出结构不对， model_res_list={model_res_list}")
    return True


def get_by_html(html_data):
    # 输入数据格式归一化
    html_data = re.sub(r'</p>\r?\n', '</p>', html_data)
    html_data = html_data.replace('</p>', '</p>\n')
    html_list = html_util.split_html(html_data)

    latex_list_with_pre_post = []
    pre_length = 6
    post_length = 6
    for data in html_list:
        data = html_util.del_html_tag(data)
        data = html_util.del_html_entities(data)

        def repl(m):
            nonlocal data
            latex_text = m.group()
            regs = m.regs[0]
            start_index = regs[0]
            end_index = regs[1]
            # latex 穿透
            pre_data = data[:start_index]
            pre_data = _clean_pre_post_data(pre_data)
            post_data = data[end_index:]
            post_data = _clean_pre_post_data(post_data)
            valid_pre_data = pre_data[-pre_length:]
            valid_pre_data = _clean_valid_pre_post_data(valid_pre_data)
            valid_post_data = post_data[:post_length]
            valid_post_data = _clean_valid_pre_post_data(valid_post_data)
            result = valid_pre_data + latex_text + valid_post_data
            # print(result)
            # if '表示' in result:
            #     print(result)
            latex_list_with_pre_post.append(result)

        re.sub(r'\$\$.*?\$\$', repl, data)

    return latex_list_with_pre_post


def _clean_pre_post_data(data):
    data = re.sub(r'\$\$.*?\$\$', '', data)
    data = re.sub(r'，+', '，', data)
    data = re.sub(r'\s+', ' ', data)
    data = re.sub(r'：\s*，\s*', '：', data)
    data = re.sub(r'、+', '、', data)
    data = re.sub(r'[“”"]', '', data)
    return data


def _clean_valid_pre_post_data(data):
    data = re.sub(r'^[\d\-，.】）；、”A-Za-z]+', '', data)
    data = re.sub(r'[\d\-，.【；（A-Za-z]+$', '', data)
    data = re.sub(r'^.，', '', data)
    return data


if __name__ == '__main__':
    subject_list = ['math']  # physics
    for s in subject_list:
        asyncio.run(main(s))
