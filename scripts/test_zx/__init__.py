import re
from collections import defaultdict
from typing import List, Set, Dict, Tuple
import numpy as np
from datasketch import MinHash, LeanMinHash, MinHashLSH
import Levenshtein
import multiprocessing as mp
import os
import pickle
from tqdm import tqdm
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.FileHandler("deduplication.log"), logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


class FuzzyDeduplicator:
    def __init__(self,
                 jaccard_threshold: float = 0.7,
                 levenshtein_threshold: int = 5,
                 n_gram: int = 3,
                 bands: int = 50,
                 rows: int = 2,
                 num_processes: int = None):
        """
        初始化模糊去重器

        参数:
            jaccard_threshold: Jaccard相似度阈值，用于初步聚类
            levenshtein_threshold: Levenshtein距离阈值，用于精确匹配
            n_gram: 生成n-gram的大小
            bands: MinHash LSH的分桶数
            rows: 每个分桶的行数
            num_processes: 并行处理的进程数，默认使用CPU核心数
        """
        self.jaccard_threshold = jaccard_threshold
        self.levenshtein_threshold = levenshtein_threshold
        self.n_gram = n_gram
        self.bands = bands
        self.rows = rows
        self.num_processes = num_processes or os.cpu_count()
        self.lsh = MinHashLSH(threshold=jaccard_threshold, num_perm=bands * rows)
        self.minhashes = {}  # 存储每个字符串的MinHash
        self.string_clusters = defaultdict(list)  # 存储聚类结果
        self.deduplicated_strings = set()  # 存储最终去重后的字符串

    def _preprocess(self, text: str) -> str:
        """预处理文本：小写化，去除标点和多余空格"""
        text = text.lower()
        text = re.sub(r'[^\w\s]', '', text)
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def _generate_ngrams(self, text: str) -> Set[str]:
        """生成文本的n-gram集合"""
        if len(text) < self.n_gram:
            return {text}
        return {text[i:i + self.n_gram] for i in range(len(text) - self.n_gram + 1)}

    def _create_minhash(self, text: str) -> MinHash:
        """为文本创建MinHash"""
        ngrams = self._generate_ngrams(text)
        mh = MinHash(num_perm=self.bands * self.rows)
        for ngram in ngrams:
            mh.update(ngram.encode('utf-8'))
        return LeanMinHash(mh)  # 使用LeanMinHash减少内存占用

    def _process_batch(self, batch: List[Tuple[int, str]]) -> Dict:
        """处理一批字符串，生成MinHash并查找候选集"""
        results = defaultdict(list)
        for idx, text in batch:
            processed_text = self._preprocess(text)
            if not processed_text:
                continue

            minhash = self._create_minhash(processed_text)
            candidates = self.lsh.query(minhash)

            # 如果没有候选集，创建一个新的聚类
            if not candidates:
                cluster_id = f"cluster_{len(self.string_clusters)}"
                self.lsh.insert(cluster_id, minhash)
                results[cluster_id].append((idx, processed_text))
            else:
                # 将字符串添加到第一个候选聚类
                cluster_id = candidates[0]
                results[cluster_id].append((idx, processed_text))

            self.minhashes[idx] = minhash

        return results

    def _levenshtein_distance_worker(self, args):
        """Levenshtein距离计算的工作函数"""
        cluster_id, cluster_items = args
        if not cluster_items:
            return []

        # 选择第一个字符串作为基准
        base_idx, base_text = cluster_items[0]
        kept_items = [cluster_items[0]]
        removed_indices = set()

        # 与基准字符串比较
        for idx, text in cluster_items[1:]:
            if Levenshtein.distance(base_text, text) <= self.levenshtein_threshold:
                removed_indices.add(idx)
            else:
                kept_items.append((idx, text))

        # 在剩余字符串中继续去重
        for i in range(len(kept_items)):
            if kept_items[i][0] in removed_indices:
                continue

            for j in range(i + 1, len(kept_items)):
                if kept_items[j][0] in removed_indices:
                    continue

                dist = Levenshtein.distance(kept_items[i][1], kept_items[j][1])
                if dist <= self.levenshtein_threshold:
                    # 保留较长的字符串
                    if len(kept_items[i][1]) >= len(kept_items[j][1]):
                        removed_indices.add(kept_items[j][0])
                    else:
                        removed_indices.add(kept_items[i][0])

        # 返回保留的字符串索引
        return [(cluster_id, idx, text) for idx, text in kept_items if idx not in removed_indices]

    def process_strings(self, strings: List[str], batch_size: int = 10000) -> None:
        """
        处理字符串列表进行模糊去重

        参数:
            strings: 待去重的字符串列表
            batch_size: 每批处理的字符串数量
        """
        logger.info(f"开始处理 {len(strings)} 个字符串")

        # 分批处理字符串
        for i in tqdm(range(0, len(strings), batch_size), desc="处理批次"):
            batch = [(idx, text) for idx, text in enumerate(strings[i:i + batch_size], start=i)]
            batch_results = self._process_batch(batch)

            # 更新聚类结果
            for cluster_id, items in batch_results.items():
                self.string_clusters[cluster_id].extend(items)

            logger.info(f"已处理批次 {i // batch_size + 1}/{(len(strings) + batch_size - 1) // batch_size}")

        logger.info(f"完成初步聚类，共生成 {len(self.string_clusters)} 个聚类")

        # 使用多进程进行精确去重
        logger.info(f"开始精确去重，使用 {self.num_processes} 个进程")
        with mp.Pool(processes=self.num_processes) as pool:
            results = list(tqdm(
                pool.imap(self._levenshtein_distance_worker, self.string_clusters.items()),
                total=len(self.string_clusters),
                desc="精确去重"
            ))

        # 收集去重结果
        for cluster_results in results:
            for _, idx, text in cluster_results:
                self.deduplicated_strings.add(text)

        logger.info(f"去重完成，从 {len(strings)} 个字符串中保留了 {len(self.deduplicated_strings)} 个唯一字符串")

    def get_deduplicated_strings(self) -> List[str]:
        """获取去重后的字符串列表"""
        return list(self.deduplicated_strings)

    def save_to_file(self, output_file: str) -> None:
        """将去重后的字符串保存到文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            for s in self.deduplicated_strings:
                f.write(s + '\n')
        logger.info(f"已将去重后的字符串保存到 {output_file}")

    def save_state(self, state_file: str) -> None:
        """保存去重器的状态到文件"""
        state = {
            'jaccard_threshold': self.jaccard_threshold,
            'levenshtein_threshold': self.levenshtein_threshold,
            'n_gram': self.n_gram,
            'bands': self.bands,
            'rows': self.rows,
            'minhashes': self.minhashes,
            'string_clusters': self.string_clusters,
            'deduplicated_strings': self.deduplicated_strings
        }
        with open(state_file, 'wb') as f:
            pickle.dump(state, f)
        logger.info(f"已将去重器状态保存到 {state_file}")

    @classmethod
    def load_state(cls, state_file: str) -> 'FuzzyDeduplicator':
        """从文件加载去重器状态"""
        with open(state_file, 'rb') as f:
            state = pickle.load(f)

        deduplicator = cls(
            jaccard_threshold=state['jaccard_threshold'],
            levenshtein_threshold=state['levenshtein_threshold'],
            n_gram=state['n_gram'],
            bands=state['bands'],
            rows=state['rows']
        )
        deduplicator.minhashes = state['minhashes']
        deduplicator.string_clusters = state['string_clusters']
        deduplicator.deduplicated_strings = state['deduplicated_strings']

        # 重建LSH索引
        for cluster_id, minhash in deduplicator.minhashes.items():
            if isinstance(cluster_id, str) and cluster_id.startswith('cluster_'):
                deduplicator.lsh.insert(cluster_id, minhash)

        logger.info(f"已从 {state_file} 加载去重器状态")
        return deduplicator


def process_large_dataset(input_file: str, output_file: str, chunk_size: int = 100000) -> None:
    """
    处理大型数据集，分块读取和处理

    参数:
        input_file: 输入文件路径，每行一个字符串
        output_file: 输出文件路径
        chunk_size: 每次处理的字符串数量
    """
    logger.info(f"开始处理大型数据集: {input_file}")

    # 初始化去重器
    deduplicator = FuzzyDeduplicator()

    # 分块处理文件
    processed_count = 0
    with open(input_file, 'r', encoding='utf-8') as f:
        while True:
            strings = []
            for _ in range(chunk_size):
                line = f.readline()
                if not line:
                    break
                strings.append(line.strip())

            if not strings:
                break

            deduplicator.process_strings(strings)
            processed_count += len(strings)
            logger.info(f"已处理 {processed_count} 个字符串")

    # 保存结果
    deduplicator.save_to_file(output_file)
    logger.info(f"大型数据集处理完成，最终保留 {len(deduplicator.get_deduplicated_strings())} 个唯一字符串")


if __name__ == "__main__":
    # 示例用法
    sample_strings = [
        "这是一个示例字符串",
        "这是一个示例字符串！",
        "这是一个测试字符串",
        "Hello, world!",
        "Hello world",
        "Python is great",
        "Python are great"
    ]

    # 处理示例数据
    deduplicator = FuzzyDeduplicator()
    deduplicator.process_strings(sample_strings)
    print(f"示例数据去重结果 ({len(sample_strings)} -> {len(deduplicator.get_deduplicated_strings())}):")
    for s in deduplicator.get_deduplicated_strings():
        print(f"- {s}")

    # 保存去重器状态
    deduplicator.save_state("deduplicator_state.pkl")

    # 加载去重器状态
    loaded_deduplicator = FuzzyDeduplicator.load_state("deduplicator_state.pkl")
    print(f"从状态加载后，去重结果数量: {len(loaded_deduplicator.get_deduplicated_strings())}")

    # 处理大型数据集示例
    process_large_dataset("large_input.txt", "large_output.txt")