# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/4/21 18:16
import collections

from app.basic.util import utils
from scripts.duplicate_check.get_similarity import FindSimilarTexts


"""
- 获取一个 json
- 遍历 json 的试题并把各字段内容合并，生成如下结构 [{node_id: node_str}, ...]
- 做重复检测，生成结构 {node_id1: [node_id2, node_id3], node_id4: [node_id5]}
"""


class NodeDuplicateCheck:
    def __init__(self, json_data):
        self.json_data = json_data

    def main(self):

        node_str_list = []
        for item in utils.iter_tree(self.json_data):
            node = item.node
            node_type = node['node_type']
            if node_type != 'question':
                continue
            content = node['content']
            node_id = node['node_id']
            node_str = self.get_node_str(content)
            node_str_list.append({
                'node_id': node_id,
                'node_str': node_str,
            })

        similar_list = FindSimilarTexts(node_str_list, target=0.8).main()
        similar_node_id_dict = collections.defaultdict(list)
        for item in similar_list:
            node_id_1, node_id_2 = item['node_id_1'], item['node_id_2']
            similar_node_id_dict[node_id_1].append(node_id_2)

        for item in utils.iter_tree(self.json_data):
            node = item.node
            node_id = node['node_id']
            node_type = node['node_type']
            if node_type != 'question':
                continue
            if node_id in similar_node_id_dict:
                item.node['similar_node'] = similar_node_id_dict[node_id]

        return self.json_data

