import json

import requests
import os

from scripts import <PERSON><PERSON><PERSON>


def get_machine_html_url(valid_subject, _type):
    html_url_list = []
    result = []
    for i in range(1, 110):
        print(i)
        if _type == 'jxw':
            task_list = jxw_task_list(i)
        else:
            task_list = fbd_task_list(i)
        for item in task_list:
            try:
                task_id = item['taskId']
                subject = item['meta']['subject']
                if subject != valid_subject:
                    continue
                machine_url = item['machineUrl']
                if not machine_url:
                    continue
                html_url = item['htmlUrl']
                if not html_url:
                    continue

                print(task_id, subject)
                html_url_list.append(machine_url)

                result.append({
                    'task_id': item['taskId'],
                    'machine_url': machine_url,
                    'html_url': html_url,
                    'app_key': item['appKey'],
                    'subject': subject,
                    'word_url': item['file']['words']['body'][0]['origin_url']
                })
            except Exception as e:
                continue

            if len(html_url_list) == 50:
                break

        if len(html_url_list) == 50:
            break

    _dir = f'./temp/'
    if not os.path.exists(_dir):
        os.makedirs(_dir)

    path = os.path.join(_dir, f"{_type}_{valid_subject}.json")
    json.dump(result, open(path, 'w', encoding='utf-8'), ensure_ascii=False)


def jxw_task_list(page: int):
    try:
        task_list = requests.get(
            url='http://xdoc.open.hexinedu.com/api/admin/task/getListByAdmin',
            params={
                'key': 'open-九学王-API',
                'page': page,
                'pageSize': 1000,
                'unpublished': False,
                'notest': False,
            },
            cookies=COOKIE
        ).json()['data']['tasks']
    except Exception as e:
        task_list = []
    return task_list


def fbd_task_list(page: int):
    try:
        task_list = requests.get(
            url='http://xdoc.open.hexinedu.com/api/admin/task/getListByAdmin',
            params={
                'page': page,
                'pageSize': 1000,
                'unpublished': False,
                'notest': False,
                'resourceType': 5,   # 5 fbd  4 word
            },
            cookies=COOKIE
        ).json()['data']['tasks']
    except Exception as e:
        task_list = []
    return task_list


if __name__ == '__main__':
    # _type = 'daily_fbd'  # daily_fbd jxw
    #
    # subject_list = ['math', 'physics', 'chemistry', 'biology', 'politics', 'history', 'geography', 'english', 'chinese']
    # for s in subject_list:
    #     get_machine_html_url(s, _type)

    _type = 'jxw'  # daily_fbd jxw

    subject_list = ['math', 'physics', 'chemistry', 'biology']
    for s in subject_list:
        get_machine_html_url(s, _type)
