# -*-coding:utf-8 -*-
# Author     ：wang<PERSON>ua
# Email      ：<EMAIL>
# Time       ：2025/2/25 20:52
import collections
import re
import json

import requests

from app.basic.api.doubao import <PERSON>ubao, DBModel
from scripts.test_title import prompt_txt

class PrepareData:

    def __init__(self, project_id_list: list):
        self.cookies = {'UBUS': 'XGEpO3RhNFFQDdZu8q7FgkCq6andxpkOv3MLxXwT41_Dy5s_Qefo8O9iSDCQ_2gE'}
        self.project_id_list = project_id_list

    def main(self):
        project_html = self.get_project_html()
        for pid, task_list in project_html.items():
            for item in task_list:
                machine_html_url = item['machine_html']
                final_html_url = item['final_html']
                task_id = item['task_id']
                subject_name = item['subject_name']
                print(machine_html_url)
                machine_title = self.get_title(machine_html_url)
                # ai_title = self.call_deepseek(machine_title, subject_name)
                print(final_html_url)
                final_title = self.get_title(final_html_url, has_level=True)

                json_data = {
                    'machine_html_url': machine_html_url,
                    'machine_title': machine_title,
                    'ai_title': '',
                    'final_html_url': final_html_url,
                    'final_title': final_title,
                }
                json.dump(json_data, open(f'./tmp/{pid}-{task_id}.json', 'w', encoding='utf-8'), ensure_ascii=False, indent=4)
                # final_str = (machine_html_url + '\n' + machine_title +
                #              '\n' + '*' * 100 + '\n' +
                #              final_title + '\n' + final_title)
                # with open(f'./tmp/{pid}-{task_id}.txt', 'w', encoding='utf-8') as fp:
                #     fp.write(final_str)

    def call_deepseek(self, machine_title, subject_name):
        prompt = prompt_txt.prompt2.format(subject=subject_name, title=machine_title)
        res = Doubao.chat(prompt, DBModel.DS_R1)
        return res

    def get_project_html(self):
        project_html = collections.defaultdict(list)
        subject_link = {
            'math': '数学',
            'chinese': '语文',
            'history': '历史',
        }
        subject_set = set()
        for pid in self.project_id_list:
            task_list = requests.get(
                url=f'http://xdoc.open.hexinedu.com/api/admin/task/getList',
                cookies=self.cookies,
                params={'projectId': pid}
            ).json()['data']['tasks']

            for t in task_list:
                machine_html = t.get('machineUrl', '')
                final_html = t.get('formattedHtmlUrl', '')
                subject_set.add(t['subject'])
                project_html[pid].append({
                    'task_id': t['taskId'],
                    'subject': t['subject'],
                    'subject_name': subject_link[t['subject']],
                    'machine_html': machine_html,
                    'final_html': final_html,
                })
        print(subject_set)
        return project_html

    def get_title(self, html_url, has_level=False):
        html_data = requests.get(html_url).content.decode('utf-8')
        chapter_list = []

        def repl(m):
            t = m.group()
            chapter_list.append(t)

        re.sub(r'<p[^<>]*?data-label="header"[^<>]*?>(((?<!</p>)[\S\s])*?)</p>', repl, html_data)

        res = []
        for c in chapter_list:
            data_level = re.search(r'data-level="(\d+)"', c).group(1)

            def repl(m):
                t = m.group()
                if '<img' in t:
                    image_text_search = re.search(r'data-image-text="(.*?)"', t)
                    if image_text_search:
                        return image_text_search.group(1)
                    else:
                        return ''
                else:
                    return ''

            text = re.sub(r'<[^<>]*?>', repl, c).strip()
            if has_level:
                text = data_level + ' ** ' + text
            res.append(text)
        return res


if __name__ == '__main__':
    PrepareData(
        project_id_list=['69215', '69759', '69545', '69707', '69762']
    ).main()
