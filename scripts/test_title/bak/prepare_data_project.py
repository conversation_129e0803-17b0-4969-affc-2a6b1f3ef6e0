# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/2/25 20:52

import requests


class PrepareData:

    def __init__(self, project_id_list: list):
        self.cookies = {'UBUS': 'XGEpO3RhNFFQDdZu8q7FgkCq6andxpkOv3MLxXwT41_Dy5s_Qefo8O9iSDCQ_2gE'}
        self.project_id_list = project_id_list

    def main(self):
        subject_link = {
            'math': '数学',
            'chinese': '语文',
            'history': '历史',
        }
        subject_set = set()
        for pid in self.project_id_list:
            project_info = requests.get(
                url=f'http://xdoc.open.hexinedu.com/api/admin/project/getOneById',
                cookies=self.cookies,
                params={'projectId': pid}
            ).json()['data']

            task_list = requests.get(
                url=f'http://xdoc.open.hexinedu.com/api/admin/task/getList',
                cookies=self.cookies,
                params={'projectId': pid}
            ).json()['data']['tasks']
            task_info = task_list[0]
            tmp_machine_html_url, tmp_task_id = task_info['machineUrl'], str(task_info['taskId'])
            parent_task_id = str(task_info['parentTaskId'])
            machine_html_url = tmp_machine_html_url.replace(tmp_task_id, parent_task_id)

            subject = project_info['subject']
            subject_name = subject_link[subject]
            subject_set.add(subject)
            html_url = project_info['htmlUrl']

            with open(f'./tmp/project_html/{pid}_{subject_name}.html', 'wb') as f:
                f.write(requests.get(html_url).content)
            with open(f'./tmp/project_html/{pid}_machine_{subject_name}.html', 'wb') as f:
                f.write(requests.get(machine_html_url).content)

            print(f"{pid} success.")

        print(subject_set)


if __name__ == '__main__':
    PrepareData(
        project_id_list=['69215', '69759', '69545', '69707', '69762']
    ).main()
