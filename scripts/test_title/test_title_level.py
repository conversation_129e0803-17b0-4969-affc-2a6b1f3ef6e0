import requests

from scripts import <PERSON><PERSON><PERSON>
from app.basic.log import logger
from worker.title_level_worker import <PERSON>LevelWorker


def main(task_id):
    res = requests.get(
        url=f'http://xdoc.open.hexinedu.com/api/admin/taskV2/getOneById',
        params={'taskId': task_id},
        cookies=COOKIE
    ).json()
    task_info = res['data']
    html_url = task_info['html']
    machine_html_url = html_url.replace('.html', '.machine.html')
    subject = task_info['meta']['subject']
    app_key = task_info['appKey']
    logger.info(f"task_id={task_id}")
    logger.info(f"subject={subject}")
    logger.info(f"app_key={app_key}")
    logger.info(f"html_url={html_url}")
    logger.info(f"machine_html_url={machine_html_url}")

    callback_info = TitleLevelWorker(
        task_id=task_id,
        html_url=machine_html_url,
        subject=subject,
        bucket_name='xdoc-stable',
        upload_path='open/fc7539b21810cd4f0f0fb620/task/20329086.html',
        is_ai_edit=True,
        is_test=True,
    ).main()
    print(callback_info)


if __name__ == '__main__':
    tid = '20359642'
    main(tid)
