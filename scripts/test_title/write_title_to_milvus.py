# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/24 16:31
import random
import requests
from pymilvus import MilvusClient, DataType


def main(title_list):
    list_entities = []
    db_name = 'header_set'
    for text in title_list:
        entity = {'text': text, 'text_vector': text}
        list_entities.append(entity)

    # 向量化
    for index, item in enumerate(list_entities):
        text_vector = item['text_vector']
        item['text_vector'] = api_embeddings(text_vector)

    # 批量插入数据库
    if list_entities:
        try:
            milvus_url = "192.168.1.53"
            client = MilvusClient(uri=f"http://{milvus_url}:19530")
            client.insert(collection_name=db_name, data=list_entities)
        except Exception as e:
            print(f"Error inserting into database: {e}")

def api_embeddings(text):
    url = "https://api.siliconflow.cn/v1/embeddings"
    payload = {
        "model": "BAAI/bge-m3",
        "input": text,
        "encoding_format": "float"
    }
    list_tokens = [
        "sk-ivorocfzbodirzyikjlbkbwecmwaupgxdfkquepgvljqiqqn", # 137
        "sk-xqjqqculezgsvllfaqyauimosuuzegotlmqpgsttglywbrpy", # 世华
        "sk-zipeniqgicpsizrnixxklwrkjkhywifssegbrhheffzzxmnb", # 152
        "sk-ojisqocguccemlifguwnnanotxwloffdhkhobgfanwzwurqo", # 镕争
        "sk-bgmgjsfwzkiczpcnrrotmmxdydtridozsehajdrugfavcnnm", # 明纯
        # "sk-hdntfcmmbjyncomlxmdzcrkboqvwlvyveezkicchoomdtnda", # 凯旋
        # "sk-zszkkcvmxeoexxgdliefomfixptpplmkvxtulhifsykijqlz", # 鹏洋
    ]
    headers = {
        "Authorization": "Bearer " + random.choice(list_tokens),
        "Content-Type": "application/json"
    }
    res = requests.request("POST", url, json=payload, headers=headers).json()['data'][0]['embedding']
    return res


if __name__ == '__main__':
    main(['2025年石家庄市初中毕业年级教学质量检测'])
