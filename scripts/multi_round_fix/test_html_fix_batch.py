import json
import os

import sys
from pathlib import Path

# 获取项目根目录（假设 worker 和 scripts 是同级目录）
project_root = str(Path(__file__).resolve().parent.parent.parent)
sys.path.append(project_root)
from worker.xdoc_html_fix_worker import XdocHtmlFixWorker
from app.basic.log import logger
from constants import BASE_DIR


class TestXdocHtmlFixBatch:

    def __init__(self, task_list, subject, tag):
        self.task_list = task_list
        self.subject = subject
        self.tag = tag

    async def main(self):
        total_stat = []
        for t in self.task_list:
            task_id = str(t['task_id'])
            machine_html_url = t['machine_url']
            # 测试 HTML 片段
            # machine_html_url = 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/task/19123911_test_html_fix.html'
            subject = t['subject']
            app_key = t['app_key']
            logger.info('*' * 100)
            logger.info(f"task_id:{task_id} subject:{subject} app_key={app_key} machine_html_url={machine_html_url}")
            callback_info = await XdocHtmlFixWorker(
                task_id=task_id,
                html_url=machine_html_url,
                subject=subject,
                app_key=app_key,
                bucket_name='xdoc-stable',
                upload_path='open/fc7539b21810cd4f0f0fb620/task/19123911.html',
                is_ai_edit=False,
                is_test=True,
                tag=self.tag,
            ).main()
            status = callback_info['status']  # 0 成功  1 失败

            if status == 0:
                stat = callback_info['stat']
                total_stat.append(stat)
                logger.info(f'{task_id} success.')
            else:
                # 失败
                logger.info(f'{task_id} 处理失败，error={callback_info["info"]}')

        # _dir = './temp'
        # if not os.path.exists(_dir):
        #     os.makedirs(_dir)
        # json.dump(total_stat, open(os.path.join(_dir, f"stat_{self.subject}.json"), 'w', encoding='utf-8'), ensure_ascii=False)


if __name__ == '__main__':
    import asyncio
    """
    跑前备忘录：
    1.修改 v1 的调用为调用 deepseek 官网（全局搜 # deepseek 官网）
    2.确定 subject list
    3.确定 tag
    4.确定跑的范围 start end
    5.注释掉除多轮外的其他 worker
    """
    # 学科分布
    start, end = 60, 80
    loop = asyncio.get_event_loop()

    def start_batch_run(subject, tag, task_id_list):
        path = os.path.join(BASE_DIR, f'scripts/multi_round_fix/temp/html_json/{tag}_{subject}.json')
        lst = json.load(open(path, 'r', encoding='utf-8'))
        lst = lst[start: end]
        for item in lst:
            task_id_list.append(str(item['task_id']))
        loop.run_until_complete(TestXdocHtmlFixBatch(lst, subject, tag).main())

    # 九学王测试
    subject_list = ['math', 'physics', 'chemistry', 'biology', 'geography', 'history', 'english', 'politics']
    # subject_list = ['biology']
    _tag = 'jxw'
    jxw_task_id_list = []
    for s in subject_list:
        start_batch_run(s, _tag, jxw_task_id_list)
    path = os.path.join(BASE_DIR, f'scripts/multi_round_fix/temp/finished_task_ids/{_tag}_task_ids.txt')
    with open(path, 'w', encoding='utf-8') as fp:
        fp.write('\n'.join(jxw_task_id_list))
    print(jxw_task_id_list)

    # 日常 fbd 数据测试
    subject_list = ['math', 'physics', 'chemistry', 'biology']
    _tag = 'daily_fbd'
    daily_fbd_task_id_list = []
    for s in subject_list:
        start_batch_run(s, _tag, daily_fbd_task_id_list)
    path = os.path.join(BASE_DIR, f'scripts/multi_round_fix/temp/finished_task_ids/{_tag}_task_ids.txt')
    with open(path, 'w', encoding='utf-8') as fp:
        fp.write('\n'.join(daily_fbd_task_id_list))
    print(daily_fbd_task_id_list)
