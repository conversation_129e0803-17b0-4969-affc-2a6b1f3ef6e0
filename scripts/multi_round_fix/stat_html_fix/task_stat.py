# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/20 10:53
from app.basic.db.mongo import mongo_cli
from app.basic.util import utils


def main(tag_list, subject_list):
    # with open('../temp/finished_task_ids/jxw_task_ids.txt', 'r', encoding='utf-8') as fp:
    #     jxw_task_id_list = fp.read().splitlines()
    # with open('../temp/finished_task_ids/daily_fbd_task_ids.txt', 'r', encoding='utf-8') as fp:
    #     daily_fbd_task_id_list = fp.read().splitlines()

    with open('./temp/task_id_list.txt', 'r', encoding='utf-8') as fp:
        task_id_list = fp.read().splitlines()
    task_id_list = task_id_list

    error_task_id_list = []
    for tag in tag_list:
        for subject in subject_list:
            result = mongo_cli['ai-util']['html_fix_task'].find(filter={
                'tag': tag, 'subject': subject
            })
            index = 0
            cost_token_1 = 0
            cost_token_2 = 0
            error_block_count_1 = 0
            error_block_count_2 = 0
            fixed_block_count_1 = 0
            fixed_block_count_2 = 0
            for item in result:
                task_id = str(item['task_id'])
                if task_id not in task_id_list:
                    continue
                try:
                    stat = item['stat']
                    cost_token_1 += stat['cost_token_1']
                    cost_token_2 += stat['cost_token_2']
                    error_block_count_1 += stat['error_block_count_1']
                    error_block_count_2 += stat['error_block_count_2']
                    fixed_block_count_1 += stat['fixed_block_count_1']
                    fixed_block_count_2 += stat['fixed_block_count_2']
                    index += 1
                except Exception as e:
                    error_task_id_list.append(task_id)

            total_cost_token = cost_token_1 + cost_token_2
            # 全部的 error_block 数量
            total_error_block_count = error_block_count_1 + (error_block_count_2 - (error_block_count_1 - fixed_block_count_1))
            # 全部的修复数量
            total_fixed_block_count = fixed_block_count_1 + fixed_block_count_2
            if total_fixed_block_count == 0:
                total_ratio = 1.0
            else:
                total_ratio = utils.round((total_fixed_block_count / total_error_block_count), 3)
            print(f'task_count={index} subject={subject} tag={tag}\n'
                  f'第一轮 cost_token:{cost_token_1} error_block_count:{error_block_count_1} '
                  f'fixed_block_count:{fixed_block_count_1} 修复率:{get_fix_rate(fixed_block_count_1, error_block_count_1)}\n'
                  f'第二轮 cost_token:{cost_token_2} error_block_count:{error_block_count_2} '
                  f'fixed_block_count:{fixed_block_count_2} 修复率:{get_fix_rate(fixed_block_count_2, error_block_count_2)}\n'
                  f'整体 cost_token:{total_cost_token} 修复率:{total_ratio}\n'
                  f'******************************************************************************'
                  )

    print(f'error_task_id_list={error_task_id_list}')


def get_fix_rate(fixed_block_count, error_block_count):
    if error_block_count == 0:
        return 1.0
    else:
        return utils.round(fixed_block_count / error_block_count, 3)


if __name__ == '__main__':
    tag_list = ['jxw']
    # 'english'
    subject_list = ['math', 'physics', 'chemistry', 'biology', 'geography', 'history', 'politics']
    main(tag_list, subject_list)

    # tag_list = ['daily_fbd']
    # subject_list = ['math', 'physics', 'chemistry', 'biology']
    # main(tag_list, subject_list)
