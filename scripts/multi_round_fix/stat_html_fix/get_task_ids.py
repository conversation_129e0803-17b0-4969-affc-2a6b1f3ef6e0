import requests
from datetime import datetime

from scripts import CO<PERSON>IE


def main():
    res = requests.get(
        url='http://xdoc.open.hexinedu.com/api/admin/task/getListByAdmin',
        params={
            'key': 'open-九学王-API',
            'page': 1,
            'pageSize': 1000,
            'unpublished': False,
            'notest': False
        },
        cookies=COOKIE
    ).json()['data']['tasks']

    task_id_list = []
    for item in res:
        subject = item['meta']['subject']
        if subject in ('english', 'chinese'):
            continue

        create_time = item['createTime'] / 1000.0
        create_time = datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')

        if '2025-05-28' in create_time:
            task_id = str(item['taskId'])
            task_id_list.append(task_id)

    print(task_id_list)
    with open('./temp/task_id_list.txt', 'w') as f:
        f.write('\n'.join(task_id_list))


if __name__ == '__main__':
    main()
