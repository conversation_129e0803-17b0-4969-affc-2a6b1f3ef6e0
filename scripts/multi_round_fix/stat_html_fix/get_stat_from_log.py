import re

s = """2025-05-26 16:29:47,434 20271459 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 13675, 'cost_token_2': 0, 'error_block_count_1': 7, 'error_block_count_2': 0, 'fixed_block_count_1': 7, 'fixed_block_count_2': 0, 'cost_time': 12}, 'latex_zx_fix': {'cost_token': 8796, 'cache_ratio': 0.631, 'cost_time': 52}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:30:17,612 20271353 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 16447, 'cost_token_2': 0, 'error_block_count_1': 8, 'error_block_count_2': 0, 'fixed_block_count_1': 8, 'fixed_block_count_2': 0, 'cost_time': 14}, 'latex_zx_fix': {'cost_token': 9505, 'cache_ratio': 0.765, 'cost_time': 39}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:30:22,043 20271377 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 14247, 'cost_token_2': 0, 'error_block_count_1': 7, 'error_block_count_2': 0, 'fixed_block_count_1': 7, 'fixed_block_count_2': 0, 'cost_time': 16}, 'latex_zx_fix': {'cost_token': 9533, 'cache_ratio': 0.695, 'cost_time': 41}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:30:33,847 20271383 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 19858, 'cost_token_2': 0, 'error_block_count_1': 10, 'error_block_count_2': 0, 'fixed_block_count_1': 10, 'fixed_block_count_2': 0, 'cost_time': 15}, 'latex_zx_fix': {'cost_token': 4794, 'cache_ratio': 0.848, 'cost_time': 53}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:31:08,450 20271245 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 61780, 'cost_token_2': 0, 'error_block_count_1': 20, 'error_block_count_2': 0, 'fixed_block_count_1': 20, 'fixed_block_count_2': 0, 'cost_time': 44}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:31:12,984 20271249 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 55066, 'cost_token_2': 0, 'error_block_count_1': 18, 'error_block_count_2': 0, 'fixed_block_count_1': 18, 'fixed_block_count_2': 0, 'cost_time': 37}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:31:17,963 20271397 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 13637, 'cost_token_2': 2350, 'error_block_count_1': 6, 'error_block_count_2': 1, 'fixed_block_count_1': 5, 'fixed_block_count_2': 1, 'cost_time': 33}, 'latex_zx_fix': {'cost_token': 6693, 'cache_ratio': 0.872, 'cost_time': 78}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:32:06,636 20271215 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 11450, 'cost_token_2': 0, 'error_block_count_1': 4, 'error_block_count_2': 0, 'fixed_block_count_1': 4, 'fixed_block_count_2': 0, 'cost_time': 27}, 'latex_zx_fix': {'cost_token': 5293, 'cache_ratio': 0.829, 'cost_time': 76}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:32:11,254 20271259 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 61648, 'cost_token_2': 3137, 'error_block_count_1': 21, 'error_block_count_2': 1, 'fixed_block_count_1': 20, 'fixed_block_count_2': 1, 'cost_time': 62}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:33:11,791 20271428 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 12415, 'cost_token_2': 0, 'error_block_count_1': 5, 'error_block_count_2': 0, 'fixed_block_count_1': 5, 'fixed_block_count_2': 0, 'cost_time': 14}, 'latex_zx_fix': {'cost_token': 12775, 'cache_ratio': 0.671, 'cost_time': 185}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:35:39,937 20271040 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 19833, 'cost_token_2': 0, 'error_block_count_1': 10, 'error_block_count_2': 0, 'fixed_block_count_1': 10, 'fixed_block_count_2': 0, 'cost_time': 26}, 'latex_zx_fix': {'cost_token': 19931, 'cache_ratio': 0.679, 'cost_time': 116}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:36:13,635 20271327 >>> stat={'latex_error_fix': {'cost_token': 766, 'cost_time': 2}, 'html_fix': {'cost_token_1': 70279, 'cost_token_2': 0, 'error_block_count_1': 26, 'error_block_count_2': 0, 'fixed_block_count_1': 26, 'fixed_block_count_2': 0, 'cost_time': 47}, 'latex_zx_fix': {'cost_token': 45949, 'cache_ratio': 0.678, 'cost_time': 195}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:36:45,101 20271031 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 17513, 'cost_token_2': 0, 'error_block_count_1': 8, 'error_block_count_2': 0, 'fixed_block_count_1': 8, 'fixed_block_count_2': 0, 'cost_time': 23}, 'latex_zx_fix': {'cost_token': 19749, 'cache_ratio': 0.711, 'cost_time': 247}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:37:48,053 20271299 >>> stat={'latex_error_fix': {'cost_token': 2191, 'cost_time': 3}, 'html_fix': {'cost_token_1': 50918, 'cost_token_2': 6331, 'error_block_count_1': 18, 'error_block_count_2': 2, 'fixed_block_count_1': 16, 'fixed_block_count_2': 1, 'cost_time': 93}, 'latex_zx_fix': {'cost_token': 58272, 'cache_ratio': 0.673, 'cost_time': 289}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:37:54,481 20271123 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 18395, 'cost_token_2': 0, 'error_block_count_1': 8, 'error_block_count_2': 0, 'fixed_block_count_1': 8, 'fixed_block_count_2': 0, 'cost_time': 21}, 'latex_zx_fix': {'cost_token': 12784, 'cache_ratio': 0.79, 'cost_time': 43}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:37:59,279 20271074 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 20460, 'cost_token_2': 2749, 'error_block_count_1': 10, 'error_block_count_2': 1, 'fixed_block_count_1': 9, 'fixed_block_count_2': 1, 'cost_time': 39}, 'latex_zx_fix': {'cost_token': 18900, 'cache_ratio': 0.685, 'cost_time': 97}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:38:05,534 20271090 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 15094, 'cost_token_2': 0, 'error_block_count_1': 7, 'error_block_count_2': 0, 'fixed_block_count_1': 7, 'fixed_block_count_2': 0, 'cost_time': 20}, 'latex_zx_fix': {'cost_token': 15294, 'cache_ratio': 0.703, 'cost_time': 86}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:38:35,111 20271178 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 18488, 'cost_token_2': 0, 'error_block_count_1': 9, 'error_block_count_2': 0, 'fixed_block_count_1': 9, 'fixed_block_count_2': 0, 'cost_time': 11}, 'latex_zx_fix': {'cost_token': 3133, 'cache_ratio': 0.735, 'cost_time': 15}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:39:15,668 20270745 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 46494, 'cost_token_2': 0, 'error_block_count_1': 16, 'error_block_count_2': 0, 'fixed_block_count_1': 16, 'fixed_block_count_2': 0, 'cost_time': 37}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:39:40,483 20271260 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 54297, 'cost_token_2': 0, 'error_block_count_1': 18, 'error_block_count_2': 0, 'fixed_block_count_1': 18, 'fixed_block_count_2': 0, 'cost_time': 59}, 'latex_zx_fix': {'cost_token': 100846, 'cache_ratio': 0.535, 'cost_time': 443}, 'latex_error_fix_2': {'cost_token': 340, 'cost_time': 2}}
2025-05-26 16:39:50,333 20271175 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 12895, 'cost_token_2': 0, 'error_block_count_1': 7, 'error_block_count_2': 0, 'fixed_block_count_1': 7, 'fixed_block_count_2': 0, 'cost_time': 13}, 'latex_zx_fix': {'cost_token': 17797, 'cache_ratio': 0.484, 'cost_time': 94}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:39:52,085 20271141 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 15880, 'cost_token_2': 0, 'error_block_count_1': 8, 'error_block_count_2': 0, 'fixed_block_count_1': 8, 'fixed_block_count_2': 0, 'cost_time': 13}, 'latex_zx_fix': {'cost_token': 11738, 'cache_ratio': 0.364, 'cost_time': 106}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:39:52,119 20270772 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 46494, 'cost_token_2': 0, 'error_block_count_1': 16, 'error_block_count_2': 0, 'fixed_block_count_1': 16, 'fixed_block_count_2': 0, 'cost_time': 34}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:39:59,481 20271173 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 23516, 'cost_token_2': 0, 'error_block_count_1': 9, 'error_block_count_2': 0, 'fixed_block_count_1': 9, 'fixed_block_count_2': 0, 'cost_time': 18}, 'latex_zx_fix': {'cost_token': 10518, 'cache_ratio': 0.704, 'cost_time': 102}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:40:41,145 20270871 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 65858, 'cost_token_2': 0, 'error_block_count_1': 21, 'error_block_count_2': 0, 'fixed_block_count_1': 21, 'fixed_block_count_2': 0, 'cost_time': 48}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:40:56,743 20270804 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 67025, 'cost_token_2': 6104, 'error_block_count_1': 21, 'error_block_count_2': 2, 'fixed_block_count_1': 19, 'fixed_block_count_2': 1, 'cost_time': 71}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:41:04,545 20270882 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 60029, 'cost_token_2': 2815, 'error_block_count_1': 19, 'error_block_count_2': 1, 'fixed_block_count_1': 18, 'fixed_block_count_2': 1, 'cost_time': 67}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:41:20,557 20270844 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 58977, 'cost_token_2': 4070, 'error_block_count_1': 20, 'error_block_count_2': 1, 'fixed_block_count_1': 19, 'fixed_block_count_2': 1, 'cost_time': 88}, 'latex_zx_fix': {'cost_token': 0, 'cache_ratio': 0}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:43:52,383 20270918 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 48571, 'cost_token_2': 2834, 'error_block_count_1': 17, 'error_block_count_2': 1, 'fixed_block_count_1': 16, 'fixed_block_count_2': 0, 'cost_time': 61}, 'latex_zx_fix': {'cost_token': 80485, 'cache_ratio': 0.558, 'cost_time': 165}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:44:38,173 20270941 >>> stat={'latex_error_fix': {'cost_token': 0, 'cost_time': 0}, 'html_fix': {'cost_token_1': 42796, 'cost_token_2': 0, 'error_block_count_1': 16, 'error_block_count_2': 0, 'fixed_block_count_1': 16, 'fixed_block_count_2': 0, 'cost_time': 25}, 'latex_zx_fix': {'cost_token': 49231, 'cache_ratio': 0.572, 'cost_time': 209}, 'latex_error_fix_2': {'cost_token': 0, 'cost_time': 0}}
2025-05-26 16:45:44,371 20270983 >>> stat={'latex_error_fix': {'cost_token': 724, 'cost_time': 2}, 'html_fix': {'cost_token_1': 67460, 'cost_token_2': 4426, 'error_block_count_1': 24, 'error_block_count_2': 1, 'fixed_block_count_1': 23, 'fixed_block_count_2': 1, 'cost_time': 100}, 'latex_zx_fix': {'cost_token': 79998, 'cache_ratio': 0.46, 'cost_time': 173}, 'latex_error_fix_2': {'cost_token': 339, 'cost_time': 1}}
2025-05-26 16:46:35,838 20270967 >>> stat={'latex_error_fix': {'cost_token': 1619, 'cost_time': 3}, 'html_fix': {'cost_token_1': 53306, 'cost_token_2': 0, 'error_block_count_1': 18, 'error_block_count_2': 0, 'fixed_block_count_1': 18, 'fixed_block_count_2': 0, 'cost_time': 58}, 'latex_zx_fix': {'cost_token': 74392, 'cache_ratio': 0.51, 'cost_time': 271}, 'latex_error_fix_2': {'cost_token': 407, 'cost_time': 2}}
2025-05-26 16:46:58,606 20271013 >>> stat={'latex_error_fix': {'cost_token': 3908, 'cost_time': 3}, 'html_fix': {'cost_token_1': 55838, 'cost_token_2': 0, 'error_block_count_1': 20, 'error_block_count_2': 0, 'fixed_block_count_1': 20, 'fixed_block_count_2': 0, 'cost_time': 28}, 'latex_zx_fix': {'cost_token': 59797, 'cache_ratio': 0.647, 'cost_time': 300}, 'latex_error_fix_2': {'cost_token': 892, 'cost_time': 4}}
"""

lst = s.split('\n')

for stat_str in lst:
    if not stat_str:
        continue
    cost_time_list = re.findall(r"'cost_time': (\d+)", stat_str)
    total_cost_time = 0
    for t in cost_time_list:
        total_cost_time += int(t)
    task_id = re.search(r'\s(2027\d+)\s>>>', stat_str).group(1)
    from app.basic.util import utils
    # print(task_id, f"{utils.round(total_cost_time / 60, 2)}min")
    print(task_id)
