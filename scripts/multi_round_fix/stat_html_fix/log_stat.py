# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/5/20 09:41
from app.basic.db.mongo import mongo_cli


def main(tag_list, subject_list):
    with open('./temp/task_id_list.txt', 'r', encoding='utf-8') as fp:
        task_id_list = fp.read().splitlines()
    task_id_list = task_id_list

    for tag in tag_list:
        for subject in subject_list:
            result = mongo_cli['ai-util']['html_fix_log'].find(filter={
                'tag': tag, 'subject': subject
            })
            error_list = []
            for item in result:
                error_info = (f'task_id={item["task_id"]}\n'
                              f'round_index={item["round_index"]}\n'
                              f'{item["error_info"]}')
                error_list.append(error_info)

            if error_list:
                with open(f'./temp/{tag}_{subject}.log', 'w', encoding='utf-8') as fp:
                    fp.write('\n\n\n'.join(error_list))


if __name__ == '__main__':
    tag_list = ['jxw', 'daily_fbd']
    subject_list = ['math', 'physics', 'chemistry', 'biology']
    main(tag_list, subject_list)

