# -*-coding:utf-8 -*-
# Author     ：wang<PERSON><PERSON>
# Email      ：<EMAIL>
# Time       ：2025/6/23 16:06
import requests
import json

from app.basic.api.xdoc import XdocApi
from scripts import COOKIE


def main(task_id):
    response = requests.get(
        url=f'http://xdoc.open.hexinedu.com/api/admin/taskV2/getOneById?taskId={task_id}',
        cookies=COOKIE
    )
    data = response.json()['data']
    html_url = data['html']
    html_data = requests.get(html_url).content.decode('utf-8')
    res = XdocApi.html_to_json(html_data)
    return json.dumps(res, ensure_ascii=False)


if __name__ == '__main__':
    task_id = '20748124'
    res = main(task_id)
    print(res)
