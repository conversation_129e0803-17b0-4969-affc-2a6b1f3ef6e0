import requests
import re

from scripts import CO<PERSON>IE
from app.basic.util import utils


def main():

    for i in range(1, 100):
        print(i)
        analysis_list = []
        response = requests.get(
            url='http://xdoc.open.hexinedu.com/api/admin/project/getListByAdmin',
            params={
                'status': 14,
                'key': '九学王',
                'isOutAdmin': False,
                'filterPriority7': False,
                'page': i,
                'pageSize': 100,
            },
            cookies=COOKIE
        )
        data = response.json()['data']['projects']
        for item in data:
            json_url = item.get('officialJsonUrl', '')
            if not json_url:
                continue

            json_data = requests.get(json_url).json()

            for j_item in utils.iter_tree(json_data):
                node = j_item.node
                analysis = node.get('content', {}).get('analysis', '')
                if not analysis:
                    continue
                tmp_lst = analysis.split('</p>')
                for t_i, t in enumerate(tmp_lst):
                    t = re.sub(r'<[^>]*?>', '', t)
                    t = t[:10]
                    tmp_lst[t_i] = t
                analysis = ' * '.join(tmp_lst)
                analysis_list.append(analysis)

        with open(f'./temp/{i}.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(analysis_list))


if __name__ == '__main__':
    main()


