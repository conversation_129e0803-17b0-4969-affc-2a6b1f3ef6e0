import requests
from datetime import datetime

from scripts import COOKIE
from app.basic.storagehandler import storage


def restore_html():
    # res = requests.get(
    #     url='http://xdoc.open.hexinedu.com/api/admin/task/getListByAdmin',
    #     params={
    #         'key': 'open-九学王-API',
    #         'page': 1,
    #         'pageSize': 1000,
    #         'unpublished': False,
    #         'notest': False
    #     },
    #     cookies=COOKIE
    # ).json()['data']['tasks']
    #
    # task_id_list = []
    # for item in res:
    #     subject = item['meta']['subject']
    #     if subject in ('english', 'chinese'):
    #         continue
    #
    #     create_time = item['createTime'] / 1000.0
    #     create_time = datetime.fromtimestamp(create_time).strftime('%Y-%m-%d %H:%M:%S')
    #
    #     if '2025-05-27' in create_time:
    #         task_id = item['taskId']
    #         task_id_list.append(task_id)

    task_id_list = [20362715, 20311897]

    for tid in task_id_list:
        print(tid)
        res = requests.get(
            url=f'http://xdoc.open.hexinedu.com/api/admin/taskV2/getOneById',
            params={
                'taskId': tid
            },
            cookies=COOKIE
        ).json()
        task_info = res['data']
        html_url = task_info['html']
        machine_html_url = html_url.replace('.html', '.machine.html')
        object_name = html_url.split('.com/')[1].split('?')[0]
        machine_object_name = machine_html_url.split('.com/')[1].split('?')[0]
        try:
            storage.copy_object_simple(
                bucket_name='xdoc-stable',
                source_key=machine_object_name,
                target_key=object_name,
            )
        except Exception as e:
            print(f'>>>>>>>>> {tid}')


if __name__ == '__main__':
    ...
    # restore_html()
