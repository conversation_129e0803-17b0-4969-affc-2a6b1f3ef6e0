import requests
import time
import json
from pathlib import Path

def html_to_json(url, max_retries=3, retry_interval=5):
    '''
    将指定URL的HTML转换为JSON
    :param url: 需要下载的HTML页面URL
    :param max_retries: 最大重试次数（默认3次）
    :param retry_interval: 重试间隔秒数（默认5秒）
    :return: 包含转换结果或错误信息的字典
    '''
    result = {'status': 'error', 'data': None}
    
    # 创建缓存目录
    cache_dir = Path(__file__).parent / 'tmp'
    cache_dir.mkdir(parents=True, exist_ok=True)
    
    for attempt in range(max_retries):
        try:
            # 1. 下载HTML内容
            response = requests.get(url, timeout=10)
            response.encoding = 'utf-8'
            response.raise_for_status()
            
            # 2. 构造请求参数
            payload = {
                'html': response.text,
            }
            
            # 3. 调用转换接口
            api_response = requests.post(
                'http://xdoc.open.hexinedu.com/api/open/transfer/html2json',
                json=payload,
                headers={'Content-Type': 'application/json', 'x-request-from': 'hexin'},
                timeout=15
            )
            
            # 4. 处理响应
            if api_response.status_code == 200:
                result['status'] = 'success'
                response_json = api_response.json()
                result['data'] = response_json.get('data', {})

                # 保存缓存文件
                cache_file = cache_dir / f'html_json.json'
                with open(cache_file, 'w', encoding='utf-8') as f:
                    json.dump(result['data'], f, ensure_ascii=False)
                    
                return result
            else:
                result['message'] = f'API响应异常，状态码：{api_response.status_code}'
                
        except requests.exceptions.RequestException as e:
            result['message'] = f'网络请求失败：{str(e)}'
        except json.JSONDecodeError:
            result['message'] = 'API返回非JSON格式响应'
        
        # 重试逻辑
        if attempt < max_retries - 1:
            time.sleep(retry_interval)
    
    return result

# 使用示例
if __name__ == '__main__':
    test_url = 'https://xdoc-stable.oss-cn-shanghai.aliyuncs.com/open/fc7539b21810cd4f0f0fb620/task/19123911.html?time=1749091787572'  # 替换为实际测试URL
    conversion_result = html_to_json(test_url)
    
    if conversion_result['status'] == 'success':
        print('转换成功，前100个字符：')
        print(str(conversion_result['data'])[:100])
    else:
        print(f'转换失败：{conversion_result['message']}')